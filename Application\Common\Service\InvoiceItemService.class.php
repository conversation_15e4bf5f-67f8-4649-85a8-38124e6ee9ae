<?php

namespace Common\Service;

/**
 * 发票项目服务类
 * 处理发票项目相关的业务逻辑
 */
class InvoiceItemService
{
    /**
     * 获取所有发票项目
     * @return array 发票项目列表
     */
    public function getAllInvoiceItems()
    {
        // 直接读取配置文件
        $configFile = APP_PATH . 'Common/Conf/invoice_item.php';
        if (file_exists($configFile)) {
            $items = include($configFile);
            if (!empty($items)) {
                return $items;
            }
        }

        // 如果配置文件不存在或为空，返回默认项目
        return $this->getDefaultInvoiceItems();
    }

    /**
     * 获取发票项目下拉菜单选项
     * @return array 格式化后的下拉菜单选项 [key => text]
     */
    public function getInvoiceItemOptions($excludeKeys = [])
    {
        $items = $this->getAllInvoiceItems();
        $options = [];

        foreach ($items as $text => $item) {
            if (!in_array($item['key'], $excludeKeys)) {
                $options[$item['key']] = $text;
            }
        }

        return $options;
    }

    /**
     * 获取前台发票项目下拉菜单选项 (排除版面费)
     * @return array 格式化后的下拉菜单选项 [key => text]
     */
    public function getFrontendInvoiceItemOptions()
    {
        return $this->getInvoiceItemOptions(['bmf']);
    }

    /**
     * 根据key获取发票项目文本
     * @param string $key 发票项目key
     * @return string 发票项目文本
     */
    public function getInvoiceItemTextByKey($key)
    {
        $items = $this->getAllInvoiceItems();

        foreach ($items as $text => $item) {
            if ($item['key'] === $key) {
                return $text;
            }
        }

        return '未知项目';
    }

    /**
     * 根据key获取发票项目代码
     * @param string $key 发票项目key
     * @return string 发票项目代码
     */
    public function getInvoiceItemCodeByKey($key)
    {
        $items = $this->getAllInvoiceItems();

        foreach ($items as $text => $item) {
            if ($item['key'] === $key) {
                return $item['code'];
            }
        }

        return '';
    }

    /**
     * 获取发票项目完整信息
     * @param string $key 发票项目key
     * @return array 发票项目完整信息 ['text' => 文本, 'code' => 代码, 'key' => 键值]
     */
    public function getInvoiceItemInfo($key)
    {
        $items = $this->getAllInvoiceItems();

        foreach ($items as $text => $item) {
            if ($item['key'] === $key) {
                return [
                    'text' => $text,
                    'code' => $item['code'],
                    'key' => $item['key']
                ];
            }
        }

        return [
            'text' => '未知项目',
            'code' => '',
            'key' => $key
        ];
    }

    /**
     * 获取默认发票项目（当配置文件为空时使用）
     * @return array 默认发票项目
     */
    private function getDefaultInvoiceItems()
    {
        return [
            '会议注册费' => [
                'code' => '3040304010000000000',
                'key' => 'hyzcf',
            ],
            '论文出版费' => [
                'code' => '3040304020000000000',
                'key' => 'lwcbf',
            ],
            '会议服务费' => [
                'code' => '3040304030000000000',
                'key' => 'hyfwf',
            ],
            '其他费用' => [
                'code' => '3040304990000000000',
                'key' => 'qtfy',
            ]
        ];
    }
}
