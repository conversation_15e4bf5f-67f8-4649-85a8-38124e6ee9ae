<extend name="public:guest_mini" />
<block name="title">
    <title>发票状态查询结果</title>
</block>

<block name="style">
    <link href="__MODULE__/GuestInvoice/invoice-detail.css" rel="stylesheet" type="text/css">
</block>

<block name="main">
    <div class="container">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="font-weight-bold">
                    <i class="fas fa-search mr-2"></i> 发票状态查询结果
                </h2>
                <p class="text-muted">查看您的发票申请状态</p>
            </div>
        </div>

        <!-- 导航链接 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center bg-light py-2">
                        <li class="breadcrumb-item"><a href="{:U('Home/GuestInvoice/index')}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{:U('Home/GuestInvoice/status')}">查询状态</a></li>
                        <li class="breadcrumb-item active">查询结果</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-12">
                <!-- 查询结果卡片 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-list-alt mr-2"></i>发票申请记录
                            </h4>
                            <span class="badge badge-primary badge-pill px-3 py-2">
                                找到 {$invoices|count} 条记录
                            </span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="thead-light">
                                    <tr>
                                        <th class="pl-4">申请编号</th>
                                        <th>发票抬头</th>
                                        <th class="text-right">金额</th>
                                        <th>申请日期</th>
                                        <th>状态</th>
                                        <th class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <volist name="invoices" id="invoice">
                                        <tr>
                                            <td class="pl-4 font-weight-bold">{$invoice.id}</td>
                                            <td>{$invoice.invoice_title}</td>
                                            <td class="text-right text-danger font-weight-bold">¥{$invoice.amount}</td>
                                            <td>{$invoice.create_time|date="Y-m-d H:i:s",###}</td>
                                            <td>
                                                <span class="badge badge-{$invoice.status_class} badge-pill px-3 py-2">{$invoice.status_text}</span>
                                            </td>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-sm btn-info view-details"
                                                    data-id="{$invoice.id}"
                                                    data-title="{$invoice.invoice_title}"
                                                    data-taxnum="{$invoice.buyer_tax_num}"
                                                    data-amount="{$invoice.amount}"
                                                    data-content="{$invoice.goods_info}"
                                                    data-email="{$invoice.buyer_email}"
                                                    data-date="{$invoice.create_time|date='Y-m-d H:i:s',###}"
                                                    data-status="{$invoice.status}">
                                                    <i class="fas fa-eye"></i> 查看详情
                                                </button>
                                            </td>
                                        </tr>
                                    </volist>
                                </tbody>
                            </table>
                        </div>

                        <div class="p-4 text-center border-top">
                            <a href="{:U('Home/GuestInvoice/status')}" class="btn btn-outline-primary mr-2">
                                <i class="fas fa-search mr-1"></i> 查询其他订单
                            </a>
                            <a href="{:U('Home/GuestInvoice/index')}" class="btn btn-primary">
                                <i class="fas fa-home mr-1"></i> 返回首页
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 帮助卡片 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-question-circle mr-2"></i>需要帮助？</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 mr-3">
                                <i class="fas fa-headset text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div>
                                <p class="mb-1">如果您对发票申请有任何疑问，请联系会议组织者或我们的支持团队。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-light">
                    <h5 class="modal-title" id="detailsModalLabel">
                        <i class="fas fa-file-invoice mr-2"></i>发票申请详情
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="invoice-detail-card">
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-hashtag mr-2"></i>申请编号</div>
                            <div class="detail-value font-weight-bold" id="modal-id"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-building mr-2"></i>发票抬头</div>
                            <div class="detail-value" id="modal-title"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-id-card mr-2"></i>税号</div>
                            <div class="detail-value" id="modal-taxnum"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-yen-sign mr-2"></i>金额</div>
                            <div class="detail-value text-danger font-weight-bold" id="modal-amount"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-file-alt mr-2"></i>内容</div>
                            <div class="detail-value" id="modal-content"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-envelope mr-2"></i>邮箱</div>
                            <div class="detail-value" id="modal-email"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-calendar-alt mr-2"></i>申请日期</div>
                            <div class="detail-value" id="modal-date"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-info-circle mr-2"></i>状态</div>
                            <div class="detail-value" id="modal-status"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</block>

<block name="scripts">
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Status mapping - using new status constants
            const statusMap = {
                '0': '<span class="badge badge-warning badge-pill">Pending</span>',
                '10': '<span class="badge badge-info badge-pill">Rejected</span>',
                '20': '<span class="badge badge-primary badge-pill">Waiting</span>',
                '30': '<span class="badge badge-success badge-pill">Success</span>',
                '40': '<span class="badge badge-danger badge-pill">Failed</span>',
                '50': '<span class="badge badge-secondary badge-pill">Voided</span>'
            };

            // Legacy status mapping for backward compatibility
            const legacyStatusMap = {
                '201': '<span class="badge badge-info badge-pill">In Review</span>',
                '210': '<span class="badge badge-primary badge-pill">OA Review</span>',
                '220': '<span class="badge badge-secondary badge-pill">Pending Issue</span>',
                '230': '<span class="badge badge-success badge-pill">Issued</span>',
                '240': '<span class="badge badge-danger badge-pill">Rejected</span>'
            };

            // View details button click event
            $('.view-details').on('click', function() {
                const id = $(this).data('id');
                const title = $(this).data('title');
                const taxnum = $(this).data('taxnum');
                const amount = $(this).data('amount');
                const content = $(this).data('content');
                const email = $(this).data('email');
                const date = $(this).data('date');
                const status = $(this).data('status');

                $('#modal-id').text(id);
                $('#modal-title').text(title);
                $('#modal-taxnum').text(taxnum);
                $('#modal-amount').text('¥' + amount);
                $('#modal-content').text(content);
                $('#modal-email').text(email);
                $('#modal-date').text(date);
                // 使用新状态映射或兼容旧状态映射
                $('#modal-status').html(statusMap[status] || legacyStatusMap[status] || '<span class="badge badge-dark badge-pill">Unknown</span>');

                $('#detailsModal').modal('show');
            });
        });
    </script>
</block>
