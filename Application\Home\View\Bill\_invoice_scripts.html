<!-- 自定义验证脚本 -->
<!-- 引入轻量级库 -->

<script>
    // 表单提交验证
    (function () {
        'use strict';

        // 在页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function () {
            // 定义静态字符串
            const ERROR_INVALID_AMOUNT = '开票金额必须大于0';
            const ERROR_EXCEED_AMOUNT = '开票金额不能超过剩余可开票金额';
            const ERROR_FULL_AMOUNT_REQUIRED = '必须全额开票';
            const MODAL_SECTION_ADDITIONAL_INFO = 'additionalInfoSection';
            // 使用PHP生成发票类型映射
            const MODAL_INVOCIE_TYPE_MAP = {
                <php>
                    $invoiceTypes = \Common\Lib\InvoiceTypeConstants::getAllTypes();
                    $typeTexts = [];
                    foreach ($invoiceTypes as $type) {
                        $typeTexts[] = "$type: '" . \Common\Lib\InvoiceTypeConstants::getTypeText($type) . "'";
                    }
                    echo implode(",\n                ", $typeTexts);
                </php>
            };

            // 获取发票策略信息
            const invoiceStrategy = {
                multipleInvoices: <notempty name="invoice_strategy.multiple_invoices">true<else />false</notempty>,
                partialInvoice: <notempty name="invoice_strategy.partial_invoice">true<else />false</notempty>,
                maxTimes: '{$invoice_strategy.max_times|default="不限制"}',
                appliedTimes: <notempty name="invoice_strategy.applied_times">{$invoice_strategy.applied_times}<else />0</notempty>,
                canApplyMore: <notempty name="invoice_strategy.can_apply_more">true<else />false</notempty>
            };

            // 缓存DOM元素 - 提高性能
            const elements = {
                // 表单元素
                form: document.querySelector('#invoiceForm'),
                amountInput: document.getElementById('amount'),
                previewBtn: document.getElementById('previewBtn'),
                confirmSubmit: document.getElementById('confirmSubmit'),
                // continueInvoiceBtn 已由 SweetAlert2 替代

                // 金额显示元素
                remainingAmountElements: document.querySelectorAll('.remaining-amount'),

                // 模态框元素
                confirmModal: document.getElementById('confirmModal'),
                additionalInfoSection: document.getElementById(MODAL_SECTION_ADDITIONAL_INFO),

                // 历史记录元素
                historySection: document.getElementById('invoice-history-section'),
                historyTbody: document.querySelector('#invoice-history-section tbody'),
                historySummary: document.querySelector('#invoice-history-section .invoice-summary .amount')
            };

            // 定义剩余可开票金额变量 - 使用数据属性获取
            // 处理不同货币符号，包括¥、$等
            const remainingAmount = elements.remainingAmountElements.length > 0 ?
                parseFloat(elements.remainingAmountElements[0].textContent.replace(/[^\d.]/g, '')) : 0;

            // 设置金额输入框的默认值为剩余可开票金额
            // 只有在不允许部分金额开票的情况下才自动填充金额
            if (elements.amountInput && remainingAmount > 0 && !invoiceStrategy.partialInvoice) {
                elements.amountInput.value = remainingAmount;
            }

            // 使用 just-validate 创建表单验证实例
            const validator = new JustValidate('#invoiceForm', {
                errorFieldCssClass: 'is-invalid',
                successFieldCssClass: 'is-valid',
                focusInvalidField: true,
                lockForm: true
            });

            // 验证金额函数 - 封装以避免重复代码
            function validateAmount(amount) {
                // 返回布尔值而不是错误消息字符串
                if (isNaN(amount) || amount <= 0) {
                    return false;
                } else if (amount > remainingAmount) {
                    return false;
                } else if (!invoiceStrategy.partialInvoice) {
                    // 如果不允许部分金额开票，则需要全额开票
                    // 使用精度处理，允许小于0.01的误差
                    const diff = Math.abs(amount - remainingAmount);
                    if (diff > 0.01) {
                        return false;
                    }
                }
                return true; // 返回true表示验证通过
            }

            // 添加表单验证规则
            validator
                .addField('#invoice_title', [
                    {
                        rule: 'required',
                        errorMessage: '请输入发票抬头'
                    }
                ])
                .addField('#buyer_tax_num', [
                    {
                        rule: 'required',
                        errorMessage: '请输入税号'
                    }
                ])
                .addField('#amount', [
                    {
                        rule: 'required',
                        errorMessage: '请输入开票金额'
                    },
                    {
                        validator: (value) => {
                            const amount = parseFloat(value);
                            return validateAmount(amount);
                        }
                    }
                ])
                .addField('#goods_info', [
                    {
                        rule: 'required',
                        errorMessage: '请选择开票项目'
                    }
                ])
                .addField('input[name="invoice_type"]', [
                    {
                        rule: 'required',
                        errorMessage: '请选择发票类型'
                    }
                ])
                .addField('#buyer_email', [
                    {
                        rule: 'required',
                        errorMessage: '请输入邮箱地址'
                    },
                    {
                        rule: 'email',
                        errorMessage: '请输入有效的邮箱地址'
                    }
                ])
                .addField('#termsCheck', [
                    {
                        rule: 'required',
                        errorMessage: '您必须同意条款和条件才能继续'
                    }
                ])
                .onSuccess((event) => {
                    // 验证成功后显示确认模态框
                    const formData = getFormData();
                    populateModalContent(formData);
                    showModal(elements.confirmModal);
                });

            // 缓存模态框元素
            const modalElements = {
                invoiceTitle: document.getElementById('modal-invoice_title'),
                buyerTaxNum: document.getElementById('modal-buyer_tax_num'),
                amount: document.getElementById('modal-amount'),
                goodsInfo: document.getElementById('modal-goods_info'),
                buyerEmail: document.getElementById('modal-buyer_email'),
                invoiceType: document.getElementById('modal-invoice_type'),
                buyerPhone: document.getElementById('modal-buyer_phone'),
                buyerAddress: document.getElementById('modal-buyer_address'),
                buyerAccount: document.getElementById('modal-buyer_account'),
                buyerAccountName: document.getElementById('modal-buyer_account_name'),
                salerCompany: document.getElementById('modal-saler_company')
            };

            // 填充模态框内容 - 优化版本
            function populateModalContent(data) {
                // 使用缓存的元素对象进行操作
                modalElements.invoiceTitle.innerText = data.invoiceTitle || '未填写';
                modalElements.buyerTaxNum.innerText = data.buyerTaxNum || '未填写';
                modalElements.amount.innerText = '¥' + (data.amount || '0.00');
                modalElements.goodsInfo.innerText = getInvoiceItemText(data.goodsInfo) || '未选择';
                modalElements.buyerEmail.innerText = data.buyerEmail || '未填写';
                modalElements.invoiceType.innerText = MODAL_INVOCIE_TYPE_MAP[data.invoiceType] || '未选择';
                modalElements.buyerPhone.innerText = data.buyerPhone || '未填写';

                // 处理附加信息部分
                if (data.buyerAddress || data.buyerAccount || data.buyerAccountName || data.salerCompany) {
                    elements.additionalInfoSection.classList.remove('d-none');
                    modalElements.buyerAddress.innerText = data.buyerAddress || '未填写';
                    modalElements.buyerAccount.innerText = data.buyerAccount || '未填写';
                    modalElements.buyerAccountName.innerText = data.buyerAccountName || '未填写';
                    modalElements.salerCompany.innerText = data.salerCompany || '未填写';
                } else {
                    elements.additionalInfoSection.classList.add('d-none');
                }
            }

            // 缓存表单元素
            const formElements = {
                invoiceTitle: document.getElementById('invoice_title'),
                buyerTaxNum: document.getElementById('buyer_tax_num'),
                amount: document.getElementById('amount'),
                goodsInfo: document.getElementById('goods_info'), // 开票项目
                buyerEmail: document.getElementById('buyer_email'),
                buyerPhone: document.getElementById('buyer_phone'),
                buyerAddress: document.getElementById('buyer_address'),
                buyerAccount: document.getElementById('buyer_account'),
                buyerAccountName: document.getElementById('buyer_account_name'),
                // 已移除的字段: salerCompany
            };

            // 获取表单数据 - 优化版本
            function getFormData() {
                // 使用缓存的表单元素对象获取值
                const invoiceTypeEl = document.querySelector('input[name="invoice_type"]:checked');

                return {
                    invoiceTitle: formElements.invoiceTitle.value.trim(),
                    buyerTaxNum: formElements.buyerTaxNum.value.trim(),
                    amount: formElements.amount.value.trim(),
                    goodsInfo: formElements.goodsInfo.value,
                    buyerEmail: formElements.buyerEmail.value.trim(),
                    invoiceType: invoiceTypeEl ? invoiceTypeEl.value : '',
                    buyerPhone: formElements.buyerPhone.value.trim() || '未填写',
                    buyerAddress: formElements.buyerAddress.value.trim() || '未填写',
                    buyerAccount: formElements.buyerAccount.value.trim() || '未填写',
                    buyerAccountName: formElements.buyerAccountName.value.trim() || '未填写'
                };
            }

            // 获取开票项目的显示文本
            function getInvoiceItemText(value) {
                // 如果没有值，直接返回空
                if (!value) return '';
                
                // 先尝试从预定义映射获取
                const itemMap = {
                    'registration': '会议注册费',
                    'publication': '论文出版费',
                    'service': '会议服务费',
                    'other': '其他费用'
                };
                
                // 如果在映射中有对应值，则返回
                if (itemMap[value]) {
                    return itemMap[value];
                }
                
                // 否则尝试从下拉菜单中获取对应的文本
                const selectElement = document.getElementById('goods_info');
                if (selectElement) {
                    for (let i = 0; i < selectElement.options.length; i++) {
                        if (selectElement.options[i].value === value) {
                            return selectElement.options[i].text;
                        }
                    }
                }
                
                // 如果都没有找到，返回原值
                return value;
            }

            // 预览按钮点击事件已移至表单提交事件中处理

            // 模态框操作函数 - 优化性能
            function showModal(modalElement) {
                $(modalElement).modal('show');
            }

            function hideModal(modalElement) {
                $(modalElement).modal('hide');
            }

            // 使用 accounting.js 格式化金额
            function formatCurrency(amount) {
                // 获取当前货币符号
                let currencySymbol = '¥'; // 默认人民币符号

                // 尝试从页面上获取当前货币符号
                if (elements.remainingAmountElements.length > 0) {
                    const text = elements.remainingAmountElements[0].textContent.trim();
                    const match = text.match(/^([^\d\s]+)/);
                    if (match && match[1]) {
                        currencySymbol = match[1];
                    } else if (text.indexOf('USD') >= 0 || text.indexOf('$') >= 0) {
                        currencySymbol = '$';
                    }
                }

                // 原始 accounting.js 的 API
                return accounting.formatMoney(amount, currencySymbol, 2, ',', '.');
            }

            // 使用 SweetAlert2 显示提示框
            function showAlert(type, title, message) {
                return Swal.fire({
                    icon: type, // 'success', 'error', 'warning', 'info', 'question'
                    title: title,
                    text: message,
                    confirmButtonText: '确定'
                });
            }

            // 使用 SweetAlert2 显示发票申请成功提示
            function showInvoiceSuccess(data, canApplyMore) {
                // 记录调试信息
                console.log('显示发票申请成功提示:', {
                    data: data,
                    canApplyMore: canApplyMore,
                    remainingAmount: data.remaining_amount
                });

                // 再次检查剩余金额，确保即使有退款也能正确判断是否可以继续开票
                // 这是一个额外的安全检查，防止退款情况下的误判
                let actualRemainingAmount = parseFloat(data.remaining_amount);
                const totalInvoiced = parseFloat(data.total_invoiced || 0);

                // 优先使用 invoice_total 字段（如果存在）
                if (data.pay_info && data.pay_info.invoice_total !== undefined) {
                    const invoiceTotal = parseFloat(data.pay_info.invoice_total);
                    // 计算实际可开票金额 = 总开票金额 - 已开票金额
                    const calculatedRemaining = Math.max(0, invoiceTotal - totalInvoiced);

                    console.log('成功模态框：使用 invoice_total 计算剩余金额:', {
                        invoiceTotal: invoiceTotal,
                        totalInvoiced: totalInvoiced,
                        calculatedRemaining: calculatedRemaining,
                        originalRemaining: actualRemainingAmount
                    });

                    // 如果计算出的实际剩余金额与当前剩余金额不一致，使用计算值
                    if (Math.abs(calculatedRemaining - actualRemainingAmount) > 0.01) {
                        console.log('成功模态框：更新剩余金额为计算值:', calculatedRemaining);
                        actualRemainingAmount = calculatedRemaining;
                        data.remaining_amount = calculatedRemaining.toFixed(2);
                    }
                }

                if (actualRemainingAmount < 0.01) {
                    console.log('实际剩余金额不足，强制设置为不可继续申请');
                    canApplyMore = false;
                    // 确保显示的剩余金额为0
                    data.remaining_amount = "0.00";
                }

                // 准备按钮配置
                const buttons = {
                    viewList: {
                        text: '查看发票列表',
                        className: 'btn btn-outline-secondary'
                    }
                };

                // 如果可以继续申请，添加继续申请按钮
                if (canApplyMore) {
                    buttons.continue = {
                        text: '继续申请发票',
                        className: 'btn btn-primary'
                    };
                }

                return Swal.fire({
                    title: '发票申请已提交成功！',
                    icon: 'success',
                    html: `
                        <div class="text-center">
                            <p class="text-muted mb-3">您的发票申请已提交成功，请等待审核。<br>您可以在发票列表中查看申请进度。</p>

                            <div class="mt-3 mb-2">
                                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-2">
                                    <span class="text-muted">当前已开票金额：</span>
                                    <span class="font-weight-bold">${formatCurrency(parseFloat(data.total_invoiced))}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">剩余可开票金额：</span>
                                    <span class="font-weight-bold text-success">${formatCurrency(parseFloat(data.remaining_amount))}</span>
                                </div>
                            </div>
                        </div>
                    `,
                    showCloseButton: true,
                    showConfirmButton: false,
                    showDenyButton: canApplyMore,
                    showCancelButton: true,
                    denyButtonText: '继续申请发票',
                    cancelButtonText: '查看发票列表',
                    denyButtonColor: '#28a745',
                    cancelButtonColor: '#6c757d',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isDenied) {
                        // 继续申请发票
                        // 重置表单验证状态
                        validator.refresh();
                        // 重置表单元素的验证状态类
                        Array.from(elements.form.elements).forEach(element => {
                            element.classList.remove('is-invalid', 'is-valid');
                        });

                        // 重置表单金额为剩余金额
                        if (elements.amountInput && parseFloat(data.remaining_amount) >= 0.01) {
                            if (!invoiceStrategy.partialInvoice) {
                                // 如果不允许部分金额开票，则设置为全额
                                elements.amountInput.value = data.remaining_amount;
                            } else {
                                // 如果允许部分金额开票，则清空输入框
                                elements.amountInput.value = '';
                            }
                        }
                    } else if (result.dismiss === Swal.DismissReason.cancel) {
                        // 查看发票列表
                        window.location.href = '{:U("Home/Bill/BillList")}';
                    }
                });
            }

            // 更新金额显示函数 - 使用 accounting.js
            function updateAmountDisplay(amount) {
                // 格式化金额
                let remainingAmount = parseFloat(amount.remaining_amount);
                const totalInvoiced = parseFloat(amount.total_invoiced || 0);

                // 优先使用 invoice_total 字段（如果存在）
                if (amount.pay_info && amount.pay_info.invoice_total !== undefined) {
                    const invoiceTotal = parseFloat(amount.pay_info.invoice_total);
                    // 计算实际可开票金额 = 总开票金额 - 已开票金额
                    const actualRemaining = Math.max(0, invoiceTotal - totalInvoiced);

                    console.log('使用 invoice_total 计算剩余金额:', {
                        invoiceTotal: invoiceTotal,
                        totalInvoiced: totalInvoiced,
                        actualRemaining: actualRemaining,
                        originalRemaining: remainingAmount
                    });

                    // 如果计算出的实际剩余金额与当前剩余金额不一致，使用计算值
                    if (Math.abs(actualRemaining - remainingAmount) > 0.01) {
                        console.log('更新剩余金额为计算值:', actualRemaining);
                        remainingAmount = actualRemaining;
                        // 同时更新原始数据，确保其他地方使用时也是正确的
                        amount.remaining_amount = actualRemaining.toFixed(2);
                    }
                }
                // 兼容旧逻辑：检查是否有退款信息
                else {
                    const hasRefund = amount.pay_info && amount.pay_info.is_refund === 1;
                    const refundTotal = hasRefund && amount.pay_info.refund_total ? parseFloat(amount.pay_info.refund_total) : 0;

                    // 如果有退款且剩余金额等于退款金额，则将剩余金额设为0
                    if (hasRefund && Math.abs(remainingAmount - refundTotal) < 0.01) {
                        console.log('检测到退款金额与剩余金额相同，将剩余金额设为0');
                        remainingAmount = 0;
                        // 同时更新原始数据，确保其他地方使用时也是正确的
                        amount.remaining_amount = "0.00";
                    }
                }

                // 如果剩余金额小于0.01，则显示为0
                const displayRemainingAmount = remainingAmount < 0.01 ? formatCurrency(0) : formatCurrency(remainingAmount);
                const displayTotalInvoiced = formatCurrency(totalInvoiced);

                // 更新所有剩余金额显示
                elements.remainingAmountElements.forEach(function (el) {
                    // 保留原始货币符号前缀，如 CNY 或 USD
                    const currText = el.textContent;
                    const currPrefix = currText.match(/^([A-Z]{3})\s+/) || ['', ''];
                    el.textContent = currPrefix[1] ? currPrefix[1] + ' ' + displayRemainingAmount : displayRemainingAmount;
                });

                // 更新历史汇总信息
                if (elements.historySummary) {
                    const currText = elements.historySummary.textContent;
                    const currPrefix = currText.match(/^([A-Z]{3})\s+/) || ['', ''];
                    elements.historySummary.textContent = currPrefix[1] ? currPrefix[1] + ' ' + displayTotalInvoiced : displayTotalInvoiced;
                }
            }

            // 确认提交按钮点击事件 - 使用AJAX提交 - 优化版本
            elements.confirmSubmit.addEventListener('click', function () {
                // 隐藏确认模态框
                hideModal(elements.confirmModal);

                // 获取表单数据
                const formData = new FormData(elements.form);

                // 显示加载指示器
                const originalBtnText = elements.confirmSubmit.innerHTML;
                elements.confirmSubmit.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> 正在提交...';
                elements.confirmSubmit.disabled = true;

                // 发送AJAX请求
                $.ajax({
                    url: '{:U($form_action . "Ajax")}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        // 恢复按钮状态
                        elements.confirmSubmit.innerHTML = originalBtnText;
                        elements.confirmSubmit.disabled = false;

                        if (response.status === 1) {
                            // 成功处理
                            // 获取数据 - 兼容线上和线下发票的不同数据结构
                            const responseData = response.order_data || response.invoice_data;

                            if (responseData) {
                                // 更新金额显示
                                updateAmountDisplay(responseData);

                                // 更新历史记录部分 - 确保有数据才更新
                                if (responseData.invoice_history) {
                                    try {
                                        updateInvoiceHistory(responseData.invoice_history);
                                    } catch (e) {
                                        console.error('更新历史记录错误:', e);
                                        // 如果更新失败，刷新页面显示最新数据
                                        setTimeout(() => {
                                            window.location.reload();
                                        }, 2000);
                                    }
                                }
                            } else {
                                console.warn('响应数据结构不完整，无法更新显示');
                            }

                            // 更新已申请次数
                            invoiceStrategy.appliedTimes += 1;

                            // 检查是否还可以继续申请
                            // 获取剩余金额
                            let remainingAmountValue = parseFloat(responseData.remaining_amount);
                            const totalInvoiced = parseFloat(responseData.total_invoiced || 0);

                            // 优先使用 invoice_total 字段（如果存在）
                            if (responseData.pay_info && responseData.pay_info.invoice_total !== undefined) {
                                const invoiceTotal = parseFloat(responseData.pay_info.invoice_total);
                                // 计算实际可开票金额 = 总开票金额 - 已开票金额
                                const actualRemaining = Math.max(0, invoiceTotal - totalInvoiced);

                                console.log('使用 invoice_total 计算剩余金额:', {
                                    invoiceTotal: invoiceTotal,
                                    totalInvoiced: totalInvoiced,
                                    actualRemaining: actualRemaining,
                                    originalRemaining: remainingAmountValue
                                });

                                // 如果计算出的实际剩余金额与当前剩余金额不一致，使用计算值
                                if (Math.abs(actualRemaining - remainingAmountValue) > 0.01) {
                                    console.log('更新剩余金额为计算值:', actualRemaining);
                                    remainingAmountValue = actualRemaining;
                                    responseData.remaining_amount = actualRemaining.toFixed(2);
                                }
                            }
                            // 兼容旧逻辑：检查是否有退款信息
                            else {
                                const hasRefund = responseData.pay_info && responseData.pay_info.is_refund === 1;
                                const refundTotal = hasRefund && responseData.pay_info.refund_total ? parseFloat(responseData.pay_info.refund_total) : 0;

                                // 如果有退款且剩余金额等于退款金额，则将剩余金额设为0
                                if (hasRefund && Math.abs(remainingAmountValue - refundTotal) < 0.01) {
                                    console.log('检测到退款金额与剩余金额相同，将剩余金额设为0');
                                    remainingAmountValue = 0;
                                    responseData.remaining_amount = "0.00";
                                }
                            }

                            const canApplyMore = checkCanApplyMore(remainingAmountValue);

                            // 继续申请按钮已由 SweetAlert2 处理
                            if (canApplyMore) {
                                // 重置表单金额为剩余金额
                                if (elements.amountInput && remainingAmountValue >= 0.01) {
                                    if (!invoiceStrategy.partialInvoice) {
                                        // 如果不允许部分金额开票，则设置为全额
                                        elements.amountInput.value = responseData.remaining_amount;
                                    } else {
                                        // 如果允许部分金额开票，则清空输入框
                                        elements.amountInput.value = '';
                                    }
                                }
                            }

                            // 使用 SweetAlert2 显示成功提示
                            showInvoiceSuccess(responseData, canApplyMore);
                        } else {
                            // 失败处理
                            // 如果是剩余金额错误，但实际已经提交成功，则跳转到成功页面
                            if (response.message && (
                                response.message.indexOf('该订单已无剩余可开票金额') !== -1 ||
                                response.message.indexOf('该转账已无剩余可开票金额') !== -1
                            )) {
                                // 跳转到成功页面
                                window.location.href = '{:U("Home/Bill/BillList")}';
                            } else if (response.message && (
                                response.message.indexOf('您无权申请该订单的发票') !== -1 ||
                                response.message.indexOf('您无权申请该转账的发票') !== -1
                            )) {
                                // 权限错误，跳转到登录页面
                                showAlert('error', '权限错误', '您的登录已过期或权限不足，请重新登录').then(() => {
                                    window.location.href = '{:U("Home/Login/index")}';
                                });
                            } else {
                                // 使用辅助函数显示错误提示
                                showAlert('error', '提交失败', response.message);
                            }
                        }
                    },
                    error: function (xhr, status, error) {
                        // 恢复按钮状态
                        elements.confirmSubmit.innerHTML = originalBtnText;
                        elements.confirmSubmit.disabled = false;

                        // 显示错误信息 - 使用辅助函数
                        showAlert('error', '网络错误', '请重试: ' + error);
                    }
                });
            });

            // 表单提交事件 - 使用 just-validate 处理
            elements.form.addEventListener('submit', function (event) {
                // 阻止默认提交 - just-validate 将处理验证和成功回调
                event.preventDefault();
                event.stopPropagation();
                // 不需要手动调用验证，just-validate 会自动处理
            });

            // 继续申请发票按钮点击事件已由 SweetAlert2 处理

            // 金额输入事件处理 - 优化版本
            if (elements.amountInput) {
                // 如果不允许部分金额开票，则默认设置为全额并禁用编辑
                if (!invoiceStrategy.partialInvoice) {
                    elements.amountInput.value = remainingAmount;
                    elements.amountInput.readOnly = true;
                    elements.amountInput.classList.add('bg-light');
                }

                // 使用防抖函数优化输入事件处理
                let inputTimeout;
                elements.amountInput.addEventListener('input', function () {
                    clearTimeout(inputTimeout);

                    // 使用防抖函数减少频繁验证
                    inputTimeout = setTimeout(() => {
                        const amount = parseFloat(this.value);
                        const errorMessage = validateAmount(amount);
                        this.setCustomValidity(errorMessage);
                    }, 300); // 300ms防抖延迟
                });
            }

            // 继续申请发票按钮点击事件已由 SweetAlert2 处理

            // 检查是否可以申请发票 - 优化版本
            function checkCanApplyMore(remainingAmount) {
                // 使用快速失败模式优化条件检查

                // 记录调试信息
                console.log('检查是否可以继续申请发票:', {
                    remainingAmount: remainingAmount,
                    multipleInvoices: invoiceStrategy.multipleInvoices,
                    appliedTimes: invoiceStrategy.appliedTimes,
                    maxTimes: invoiceStrategy.maxTimes
                });

                // 1. 剩余金额检查 - 使用精度处理，避免浮点数计算误差
                if (parseFloat(remainingAmount) < 0.01) {
                    console.log('剩余金额不足，无法继续申请');
                    return false;
                }

                // 2. 多张发票检查 - 如果不允许多张发票，但已申请次数为0，则允许申请
                if (!invoiceStrategy.multipleInvoices && invoiceStrategy.appliedTimes > 0) {
                    console.log('不允许多张发票，且已申请过，无法继续申请');
                    return false;
                }

                // 3. 最大申请次数检查
                if (invoiceStrategy.maxTimes !== '\u4e0d\u9650\u5236') {
                    const maxTimes = parseInt(invoiceStrategy.maxTimes);
                    if (invoiceStrategy.appliedTimes >= maxTimes) {
                        console.log('已达到最大申请次数，无法继续申请');
                        return false;
                    }
                }

                console.log('可以继续申请发票');
                return true;
            }

            // 检查是否可以申请发票
            const canApplyMore = checkCanApplyMore(remainingAmount);

            // 如果不能申请发票，禁用表单 - 优化版本
            if (!canApplyMore) {
                // 生成提示消息
                const messages = [];
                if (!invoiceStrategy.multipleInvoices) {
                    // \u6839\u636e\u8868\u5355\u52a8\u4f5c\u5224\u65ad\u662f\u8ba2\u5355\u8fd8\u662f\u8f6c\u8d26
                    const isTransfer = '{$form_action}'.indexOf('Offline') !== -1;
                    messages.push(isTransfer ? '\u60a8\u53ea\u80fd\u4e3a\u6b64\u8f6c\u8d26\u7533\u8bf7\u4e00\u5f20\u53d1\u7968\uff0c\u5df2\u8fbe\u5230\u4e0a\u9650\u3002' : '\u60a8\u53ea\u80fd\u4e3a\u6b64\u8ba2\u5355\u7533\u8bf7\u4e00\u5f20\u53d1\u7968\uff0c\u5df2\u8fbe\u5230\u4e0a\u9650\u3002');
                }
                if (invoiceStrategy.maxTimes !== '\u4e0d\u9650\u5236' && invoiceStrategy.appliedTimes >= parseInt(invoiceStrategy.maxTimes)) {
                    messages.push(`\u60a8\u5df2\u7533\u8bf7 ${invoiceStrategy.appliedTimes} \u5f20\u53d1\u7968\uff0c\u5df2\u8fbe\u5230\u4e0a\u9650\u3002`);
                }
                if (parseFloat(remainingAmount) < 0.01) {
                    // \u6839\u636e\u8868\u5355\u52a8\u4f5c\u5224\u65ad\u662f\u8ba2\u5355\u8fd8\u662f\u8f6c\u8d26
                    const isTransfer = '{$form_action}'.indexOf('Offline') !== -1;
                    messages.push(isTransfer ? '\u60a8\u7684\u8f6c\u8d26\u5df2\u5168\u989d\u5f00\u7968\uff0c\u65e0\u6cd5\u7ee7\u7eed\u7533\u8bf7\u3002' : '\u60a8\u7684\u8ba2\u5355\u5df2\u5168\u989d\u5f00\u7968\uff0c\u65e0\u6cd5\u7ee7\u7eed\u7533\u8bf7\u3002');
                }

                // 使用模板字符串生成提示信息
                const alertHtml = `
                    <div class="alert alert-warning alert-dismissible fade show mt-3" role="alert">
                        <div class="d-flex">
                            <div class="mr-3">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading mb-1">无法申请发票</h5>
                                <p class="mb-0">${messages.join(' ')}</p>
                            </div>
                        </div>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                `;

                // 使用缓存的元素对象
                const formElement = document.getElementById('invoiceForm');
                formElement.insertAdjacentHTML('beforebegin', alertHtml);

                // 使用更高效的方式禁用表单
                formElement.setAttribute('disabled', 'disabled');
                Array.from(formElement.elements).forEach(element => {
                    element.disabled = true;
                });

                // 禁用提交按钮
                elements.previewBtn.disabled = true;
            }

            // 使用原生 DOM 操作替代 lit-html
            // 由于 lit-html 加载问题，我们使用原生 DOM 操作

            // 状态样式映射表 - 避免重复的switch语句
            const statusMap = {
                0: { class: 'badge-warning', text: '待审核' },
                201: { class: 'badge-info', text: '审核中' },
                210: { class: 'badge-primary', text: 'OA审核' },
                220: { class: 'badge-secondary', text: '待开票' },
                230: { class: 'badge-success', text: '已开票' },
                290: { class: 'badge-danger', text: '已作废' },
                298: { class: 'badge-danger', text: '已作废' },
                299: { class: 'badge-danger', text: '已作废' }
            };

            // 格式化日期函数
            function formatDate(timestamp) {
                const date = new Date(timestamp * 1000);
                return date.getFullYear() + '-' +
                    ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                    ('0' + date.getDate()).slice(-2);
            }

            // 创建历史记录行函数 - 使用原生 DOM
            function createInvoiceRow(invoice) {
                const tr = document.createElement('tr');
                const status = statusMap[parseInt(invoice.status)] || { class: 'badge-secondary', text: '未知' };

                // 创建日期单元格
                const tdDate = document.createElement('td');
                tdDate.textContent = formatDate(invoice.create_time);
                tr.appendChild(tdDate);

                // 创建发票抬头单元格
                const tdTitle = document.createElement('td');
                tdTitle.textContent = invoice.invoice_title;
                tr.appendChild(tdTitle);

                // 创建金额单元格
                const tdAmount = document.createElement('td');
                tdAmount.textContent = formatCurrency(parseFloat(invoice.amount));
                tr.appendChild(tdAmount);

                // 创建状态单元格
                const tdStatus = document.createElement('td');
                const span = document.createElement('span');
                span.className = `badge badge-pill ${status.class}`;
                span.textContent = status.text;
                tdStatus.appendChild(span);
                tr.appendChild(tdStatus);

                return tr;
            }

            // 更新发票历史记录函数 - 使用原生 DOM
            function updateInvoiceHistory(invoiceHistory) {
                // 使用缓存的元素
                if (!elements.historySection) return;

                // 显示历史记录部分
                elements.historySection.style.display = invoiceHistory && invoiceHistory.length > 0 ? 'block' : 'none';

                if (!invoiceHistory || invoiceHistory.length === 0) return;

                // 使用缓存的表格体
                if (!elements.historyTbody) return;

                // 清空现有内容
                elements.historyTbody.innerHTML = '';

                // 使用文档片段优化DOM操作
                const fragment = document.createDocumentFragment();

                // 添加新的历史记录
                invoiceHistory.forEach(function(invoice) {
                    const row = createInvoiceRow(invoice);
                    fragment.appendChild(row);
                });

                // 一次性添加所有行
                elements.historyTbody.appendChild(fragment);

                // 更新汇总信息 - 使用缓存的元素
                if (elements.historySummary) {
                    elements.historySummary.textContent = formatCurrency(parseFloat(invoiceHistory[0].total_invoiced || 0));
                }
            }
        });
    })();
</script>

