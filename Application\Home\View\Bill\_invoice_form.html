<div class="card">
    <div class="card-body">
        <h4 class="card-title">发票申请表单</h4>
        <p class="card-subtitle mb-2">请填写以下信息申请发票，带 <code>*</code> 的字段为必填项</p>

        <!-- 发票策略提示信息 -->
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <div class="d-flex">
                <div class="mr-3">
                    <i class="fas fa-info-circle fa-2x"></i>
                </div>
                <div>
                    <h5 class="alert-heading mb-1">发票申请说明</h5>
                    <p class="mb-0">
                        <notempty name="invoice_strategy.multiple_invoices">
                            您可以为此订单申请<strong>多张发票</strong>
                            <if condition="$invoice_strategy.max_times neq '\u4e0d\u9650\u5236'">
                                （最多 {$invoice_strategy.max_times} 张）
                            </if>
                            <else />
                            您只能为此订单申请<strong>一张发票</strong>
                        </notempty>
                        <notempty name="invoice_strategy.partial_invoice">
                            ，且可以<strong>部分金额开票</strong>
                            <else />
                            ，且必须<strong>全额开票</strong>
                        </notempty>
                        。当前已申请 <strong class="text-primary">{$invoice_strategy.applied_times|default='0'}</strong>
                        张发票。
                    </p>
                </div>
            </div>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>

        <form id="invoiceForm" action="{:U($form_action)}" method="post">
            <input type="hidden" name="order_id" value="{$pay_info.orderid|default=''}">
            <input type="hidden" name="reg_id" value="{$reg_id}">

            <!-- 基本信息 这里要实现输入关键词，自动搜索出相关数据并填充抬头和税号的功能 -->
            <div class="form-section">
                <h5 class="form-section-title">基本信息</h5>
                <div class="form-row-flex">
                    <div class="form-col-flex">
                        <div class="form-group">
                            <label for="invoice_title">发票抬头 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="invoice_title" name="invoice_title"
                                placeholder="请输入发票抬头" required value="{$user_info.invoice_title|default=''}">

                        </div>
                    </div>
                    <div class="form-col-flex">
                        <div class="form-group">
                            <label for="buyer_tax_num">税号 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="buyer_tax_num" name="buyer_tax_num"
                                placeholder="请输入税号" required value="{$user_info.buyer_tax_num|default=''}">

                        </div>
                    </div>
                </div>

                <div class="form-row-flex">
                    <div class="form-col-flex">
                        <div class="form-group">
                            <label for="amount">开票金额 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <notempty name="transfer_info.currency_symbol">
                                            {$transfer_info.currency_symbol}
                                            <else />
                                            ¥
                                        </notempty>
                                    </span>
                                </div>
                                <input type="number" step="0.01" min="0.01" id="amount" name="amount"
                                    placeholder="请输入开票金额" required class="form-control amount-display" value="">

                            </div>
                            <small class="form-text text-muted">剩余可开票金额:
                                <span class="remaining-amount">
                                    <notempty name="transfer_info.currency_symbol">
                                        {$transfer_info.currency_symbol}
                                        <else />
                                        ¥
                                    </notempty>
                                    {$remaining_amount}
                                </span>
                            </small>

                            <!-- 金额说明提示 -->
                            <if condition="null !== $pay_info['is_refund'] && $pay_info['is_refund'] eq 1">
                                <small class="form-text text-danger mt-1">
                                    <i class="fas fa-info-circle mr-1"></i>由于订单有退款记录，可开票金额已调整为订单总额减去退款金额
                                </small>
                            </if>
                            <if condition="$total_invoiced gt 0">
                                <small class="form-text text-info mt-1">
                                    <i class="fas fa-info-circle mr-1"></i>您已申请过发票，可开票金额为剩余未开票部分
                                </small>
                            </if>
                        </div>
                    </div>
                    <div class="form-col-flex">
                        <div class="form-group">
                            <label for="goods_info">开票项目 <span class="text-danger">*</span></label>
                            <select class="form-control" id="goods_info" name="goods_info" required>
                                <foreach name="invoice_item_options" item="text" key="key">
                                    <option value="{$key}" <if condition="isset($user_info['goods_info']) && $user_info['goods_info'] == $key">selected</if>>{$text}</option>
                                </foreach>
                            </select>

                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>发票类型 <span class="text-danger">*</span></label>
                    <div>
                        <php>
                            $invoiceTypes = \Common\Lib\InvoiceTypeConstants::getAllTypes();
                            $invoiceTypeTexts = [];
                            foreach ($invoiceTypes as $type) {
                            $invoiceTypeTexts[$type] = \Common\Lib\InvoiceTypeConstants::getTypeText($type);
                            }
                        </php>
                        <foreach name="invoiceTypeTexts" item="text" key="type">
                            <div class="custom-control custom-radio custom-control-inline">
                                <input type="radio" id="invoiceType{$type|ucfirst}" name="invoice_type"
                                    class="custom-control-input" value="{$type}" required>
                                <label class="custom-control-label" for="invoiceType{$type|ucfirst}">{$text}</label>
                            </div>
                        </foreach>

                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <h5 class="form-section-title">联系信息</h5>
                <div class="form-row-flex">
                    <div class="form-col-flex">
                        <div class="form-group">
                            <label for="buyer_phone">买方手机号</label>
                            <input type="tel" class="form-control" id="buyer_phone" name="buyer_phone"
                                placeholder="请输入手机号码" value="{$user_info.buyer_phone|default=''}">
                        </div>
                    </div>
                    <div class="form-col-flex">
                        <div class="form-group">
                            <label for="buyer_email">买方邮箱 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="buyer_email" name="buyer_email"
                                placeholder="请输入邮箱地址" required
                                value="<notempty name='pay_info.email'>{$pay_info.email}<else /><notempty name='transfer_info.email'>{$transfer_info.email}<else /></notempty></notempty>">
                            <div class="invalid-feedback">
                                请输入有效的邮箱地址
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 补充信息 -->
            <div class="form-section">
                <button type="button" class="btn btn-link text-primary p-0" data-toggle="collapse"
                    data-target="#moreInfoFields" aria-expanded="false" aria-controls="moreInfoFields">
                    <i class="feather-plus-circle mr-1"></i> 补充更多信息(联系电话,单位地址,开户行等)
                </button>

                <div class="collapse mt-3" id="moreInfoFields">
                    <div class="bg-light py-4 px-4 mb-3" style="border-radius: 4px;">
                        <h5 class="form-section-title">
                            附加信息
                            <a href="javascript:void(0)" class="ml-1 text-primary" data-toggle="tooltip"
                                data-placement="top" title="如果你需要将单位的账户、地址、电话等显示在发票备注栏，请填写。">
                                <i class="feather-help-circle"></i>
                            </a>
                        </h5>

                        <div class="form-group mb-4">
                            <label for="buyer_tel">座机电话号码</label>
                            <input type="tel" class="form-control" id="buyer_tel" name="buyer_tel"
                                placeholder="请输入座机电话号码（如：010-12345678）" value="{$user_info.buyer_tel|default=''}">
                        </div>

                        <div class="form-group mb-4">
                            <label for="buyer_address">单位地址</label>
                            <textarea class="form-control" id="buyer_address" name="buyer_address" rows="2"
                                placeholder="请输入单位地址"></textarea>
                        </div>

                        <div class="form-group mb-4">
                            <label for="buyer_account">单位账户号</label>
                            <input type="text" class="form-control" id="buyer_account" name="buyer_account"
                                placeholder="请输入单位账户号">
                        </div>

                        <div class="form-group mb-4">
                            <label for="buyer_account_name">单位开户行</label>
                            <input type="text" class="form-control" id="buyer_account_name" name="buyer_account_name"
                                placeholder="请输入单位开户行">
                        </div>
<!-- 
                        <div class="form-group mb-4">
                            <label for="remark">备注信息</label>
                            <textarea class="form-control" id="remark" name="remark" rows="3"
                                placeholder="请输入备注信息（可选）" value="{$user_info.remark|default=''}"></textarea>
                        </div>

       
                        <input type="hidden" id="saler_company" name="saler_company" value=""> -->
                    </div>
                </div>
            </div>

            <div class="form-group mt-4">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="termsCheck" required>
                    <label class="custom-control-label" for="termsCheck">我确认以上开票信息准确无误</label>

                </div>
            </div>

            <div class="text-right mt-4">
                <a href="{:U($back_url)}" class="btn btn-outline-secondary mr-2">返回</a>
                <button type="submit" id="previewBtn" class="btn btn-primary px-4">预览并提交</button>
            </div>
        </form>
    </div>
</div>

<script src="__ROOT__/static/js/axios.min.js"></script>

<script>
    class CompanySearch {
        constructor(options = {}) {
            this.companyInputId = options.companyInputId;
            this.taxNumberInputId = options.taxNumberInputId;
            this.minLength = options.minLength || 2;
            this.debounceTime = options.debounceTime || 500;
            this.onSelect = options.onSelect;
            this.init();
        }

        init() {
            this.companyInput = document.getElementById(this.companyInputId);
            this.taxNumberInput = document.getElementById(this.taxNumberInputId);
            if (!this.companyInput || !this.taxNumberInput) return;

            this.createDropdown();
            this.bindEvents();
        }

        createDropdown() {
            this.dropdown = document.createElement('ul');
            this.dropdown.className = 'dropdown-menu w-100';
            Object.assign(this.dropdown.style, {
                maxHeight: '200px',
                overflowY: 'auto',
                display: 'none',
                position: 'absolute',
                width: '100%',
                zIndex: '1000'
            });

            this.companyInput.parentNode.style.position = 'relative';
            this.companyInput.parentNode.appendChild(this.dropdown);
        }

        bindEvents() {
            let timer;
            this.companyInput.addEventListener('input', () => {
                clearTimeout(timer);
                timer = setTimeout(() => this.searchCompanies(), this.debounceTime);
            });

            document.addEventListener('click', (e) => {
                if (!this.companyInput.contains(e.target) && !this.dropdown.contains(e.target)) {
                    this.hideDropdown();
                }
            });
        }

        hideDropdown() {
            this.dropdown.style.display = 'none';
        }

        async searchCompanies() {
            const searchText = this.companyInput.value.trim();
            if (searchText.length < this.minLength) {
                this.hideDropdown();
                return;
            }

            try {
                const response = await axios.post('/Home/Invoice/queryCompanyInfo', {
                    prefix: searchText
                });

                if (response.data.success) {
                    this.updateCompanies(response.data.data);
                }
            } catch (error) {
                this.hideDropdown();
            }
        }

        updateCompanies(companies) {
            this.dropdown.innerHTML = '';

            if (!companies?.length) {
                this.hideDropdown();
                return;
            }

            companies.forEach(company => {
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.className = 'dropdown-item';
                a.href = '#';
                a.textContent = company.name;
                a.onclick = (e) => {
                    e.preventDefault();
                    this.selectCompany(company);
                };
                li.appendChild(a);
                this.dropdown.appendChild(li);
            });

            this.dropdown.style.display = 'block';
        }

        async selectCompany(company) {
            this.companyInput.value = company.name;
            this.hideDropdown();

            try {
                const response = await axios.post('/Home/Invoice/getCompanyDetail', {
                    code: company.code
                });

                if (response.data.success) {
                    const detail = response.data.data;
                    this.taxNumberInput.value = detail.kpCode;
                    this.taxNumberInput.readOnly = true;

                    if (this.onSelect) {
                        this.onSelect(detail);
                    }
                }
            } catch (error) {
                this.taxNumberInput.readOnly = false;
            }
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        new CompanySearch({
            companyInputId: 'invoice_title',
            taxNumberInputId: 'buyer_tax_num',
            minLength: 2,
            debounceTime: 500,
            onSelect: function (detail) {
                const invoiceType = document.querySelector('input[name="invoice_type"]:checked')?.value;
                if (invoiceType === 'bs') {
                    const inputs = {
                        bank_name: document.getElementById('bank_name'),
                        bank_account: document.getElementById('bank_account'),
                        buyer_address: document.getElementById('buyer_address'),
                        contact_tel: document.getElementById('contact_tel')
                    };

                    if (inputs.bank_name) inputs.bank_name.value = detail.bankName || '';
                    if (inputs.bank_account) inputs.bank_account.value = detail.bankAccount || '';
                    if (inputs.buyer_address) inputs.buyer_address.value = detail.address || '';
                    if (inputs.contact_tel) inputs.contact_tel.value = detail.phone || '';
                }
            }
        });
    });
</script>