<?php
// 用户账单发票控制器
// http://iconfsys.cc/home/<USER>
//http://iconfsys.cc/home/<USER>/index/order_id/20241230-897438777-154410223504-IC-CN

namespace Home\Controller;

use Common\Lib\TransferStatusConstants as TransferStatus;
use Common\Lib\InvoiceStatusConstants as Status;
use Common\Lib\OnlinePaymentStatusConstants;
use Common\Lib\InvoiceTypeConstants;
use Home\Helper\InvoiceHelper;

class BillController extends BillBaseController
{
    //初始化
    public function _initialize()
    {
        parent::_initialize();

        // 加载支付同步服务类
        $this->paymentSyncService = new \Home\Service\PaymentSyncService();
    }

    /**
     * 兼容旧的URL路径，重定向到verifyOrder方法
     */
    public function index()
    {
        $order_id = I('get.order_id');
        $this->redirect('verifyOrder', ['order_id' => $order_id]);
    }

    /**
     * 订单核验页面
     * 显示订单核验表单，用户需要先通过核验才能进入发票申请页面
     */
    public function verifyOrder()
    {
        // 获取订单号
        $order_id = I('get.order_id');

        // 将订单号传递给视图
        $this->assign('order_id', $order_id);

        // 启用发票样式
        $this->assign('invoice_css', true);

        // 显示核验页面
        $this->display();
    }

    /**
     * 处理订单核验表单提交
     * 验证订单是否可以开票，并将结果存储在session中
     */
    public function verifyOrderSubmit()
    {
        try {
            // 获取订单号
            $order_id = I('post.order_id');

            if (empty($order_id)) {
                $this->error('请输入订单号');
                return;
            }

            // 记录调试信息
            \Think\Log::record('开始核验订单: ' . $order_id, 'INFO');

            // 使用支付查询服务获取订单详情
            $queryService = new \Common\Service\PaymentQueryService();
            $result = $queryService->queryOrderDetail($order_id);

            // 如果查询失败
            if (!$result['success']) {
                \Think\Log::record('订单核验失败: ' . $result['message'], 'WARN');

                // 准备错误结果
                $verification_result = [
                    'success' => false,
                    'class' => 'error',
                    'title' => '订单核验失败',
                    'message' => $result['message'],
                    'order_id' => $order_id
                ];

                $this->assign('verification_result', $verification_result);
                $this->assign('invoice_css', true);
                $this->assign('order_id', $order_id); // 保持订单号在表单中显示
                $this->display('verifyOrder');
                return;
            }

            // 获取订单信息
            $orderData = $result['data'];

            // 检查订单是否可以开票
            if (!$orderData['can_invoice']) {
                \Think\Log::record('订单不可开票: ' . $order_id . ', 原因: ' . $orderData['invoice_reason'], 'WARN');

                // 检查是否是全额退款 - 多种判断方式
                $is_full_refund = false;

                // 1. 通过金额比较判断
                if (isset($orderData['refund_amount']) && isset($orderData['total'])) {
                    $refund_amount = floatval($orderData['refund_amount']);
                    $total_amount = floatval($orderData['total']);
                    $is_full_refund = ($refund_amount >= $total_amount * 0.999);
                }

                // 2. 通过invoice_reason判断
                $refund_keywords = ['全额退款', '已经全额退款', '已全额退款'];
                foreach ($refund_keywords as $keyword) {
                    if (strpos($orderData['invoice_reason'], $keyword) !== false) {
                        $is_full_refund = true;
                        break;
                    }
                }

                // 3. 通过invoice_amount判断
                if (isset($orderData['invoice_amount']) && floatval($orderData['invoice_amount']) <= 0) {
                    $is_full_refund = true;
                }

                // 记录全额退款判断的详细日志
                \Think\Log::record('全额退款判断: ' . $order_id .
                    ', 通过金额比较=' . (isset($orderData['refund_amount']) && isset($orderData['total']) && floatval($orderData['refund_amount']) >= floatval($orderData['total']) * 0.999 ? 'true' : 'false') .
                    ', 通过关键词=' . (strpos($orderData['invoice_reason'], '全额退款') !== false ? 'true' : 'false') .
                    ', 通过invoice_amount=' . (isset($orderData['invoice_amount']) && floatval($orderData['invoice_amount']) <= 0 ? 'true' : 'false') .
                    ', 最终判断=' . ($is_full_refund ? 'true' : 'false'), 'INFO');

                // 如果是全额退款，使用特殊的显示样式
                if ($is_full_refund) {
                    // 获取订单金额信息
                    $total = isset($orderData['total']) ? floatval($orderData['total']) : 0;
                    $refund_amount = isset($orderData['refund_amount']) ? floatval($orderData['refund_amount']) : 0;

                    // 准备全额退款结果，包含金额信息
                    $verification_result = [
                        'success' => false,
                        'class' => 'danger',
                        'title' => '订单已全额退款',
                        'message' => '该订单已全额退款，无法申请发票',
                        'order_id' => $order_id,
                        'is_full_refund' => true,
                        'total_amount' => number_format($total, 2, '.', ''),
                        'refund_amount' => number_format($refund_amount, 2, '.', ''),
                        'invoiced_amount' => '0.00',
                        'remaining_amount' => '0.00'
                    ];

                    // 记录使用全额退款显示样式
                    \Think\Log::record('使用全额退款显示样式: ' . $order_id . ', 订单总额=' . $total . ', 退款金额=' . $refund_amount, 'INFO');
                } else {
                    // 准备普通警告结果
                    $verification_result = [
                        'success' => false,
                        'class' => 'warning',
                        'title' => '订单不可开票',
                        'message' => $orderData['invoice_reason'],
                        'order_id' => $order_id
                    ];
                }

                $this->assign('verification_result', $verification_result);
                $this->assign('invoice_css', true);
                $this->assign('order_id', $order_id); // 保持订单号在表单中显示
                $this->display('verifyOrder');
                return;
            }

            // 检查货币类型是否允许开票
            if (!isset($orderData['currency']) || !\Common\Lib\InvoiceEligibilityConfig::isCurrencyAllowed($orderData['currency'] === 'CNY' ? 0 : 1)) {
                \Think\Log::record('订单货币类型不支持开票: ' . $order_id . ', 货币类型: ' . $orderData['currency'], 'WARN');

                // 准备警告结果
                $verification_result = [
                    'success' => false,
                    'class' => 'warning',
                    'title' => '订单货币类型不支持开票',
                    'message' => '抱歉，目前仅支持人民币订单申请发票',
                    'order_id' => $order_id
                ];

                $this->assign('verification_result', $verification_result);
                $this->assign('invoice_css', true);
                $this->assign('order_id', $order_id); // 保持订单号在表单中显示
                $this->display('verifyOrder');
                return;
            }

            // 订单核验成功，将订单标记为已核验（保存到数据库）
            $model_pay = M('subPay');
            $db_pay_info = $model_pay->where(['orderid' => $order_id])->find();

            if (!$db_pay_info) {
                \Think\Log::record('订单不存在于数据库中: ' . $order_id, 'ERROR');
                throw new \Exception('订单不存在于数据库中');
            }

            // 计算可开票金额 = 订单总金额 - 退款金额
            $total = floatval($orderData['total']);
            $refund_amount = floatval($orderData['refund_amount']);
            $invoice_total = $total - $refund_amount;

            // 确保可开票金额不为负数
            if ($invoice_total < 0) {
                $invoice_total = 0;
            }

            // 记录计算的可开票金额
            \Think\Log::record('计算的可开票金额: ' . $invoice_total . ' (total=' . $total . ', refund_amount=' . $refund_amount . ')', 'INFO');

            // 更新数据库中的核验状态和可开票金额
            $updateData = [
                'is_check' => 1,
                'check_time' => time(),
                'invoice_total' => number_format($invoice_total, 2, '.', '') // 添加可开票金额字段
            ];

            // 记录数据库中的退款信息
            \Think\Log::record('数据库中的退款信息: is_refund=' .
                (isset($db_pay_info['is_refund']) ? $db_pay_info['is_refund'] : 'null') .
                ', refund_total=' . (isset($db_pay_info['refund_total']) ? $db_pay_info['refund_total'] : 'null'), 'DEBUG');

            // 合并数据库中的退款信息到orderData
            if (isset($db_pay_info['is_refund']) && $db_pay_info['is_refund'] == 1) {
                $orderData['is_refund'] = $db_pay_info['is_refund'];
                $orderData['refund_total'] = $db_pay_info['refund_total'];
                $orderData['refund_count'] = isset($db_pay_info['refund_count']) ? $db_pay_info['refund_count'] : 1;

                // 如果数据库中有退款信息，使用数据库中的退款金额重新计算可开票金额
                $refund_total = floatval($db_pay_info['refund_total']);
                $invoice_total = $total - $refund_total;

                // 确保可开票金额不为负数
                if ($invoice_total < 0) {
                    $invoice_total = 0;
                }

                // 更新可开票金额
                $updateData['invoice_total'] = number_format($invoice_total, 2, '.', '');

                // 记录使用数据库退款信息重新计算的可开票金额
                \Think\Log::record('使用数据库退款信息重新计算的可开票金额: ' . $invoice_total . ' (total=' . $total . ', refund_total=' . $refund_total . ')', 'INFO');

                // 记录合并后的退款信息
                \Think\Log::record('合并后的退款信息: is_refund=' . $orderData['is_refund'] .
                    ', refund_total=' . $orderData['refund_total'], 'DEBUG');
            }

            // 获取发票历史记录
            $invoice_model = M('OaInvoice');
            $invoice_history = $invoice_model->where(['order_id' => $order_id])->select();

            // 计算已开票金额
            $total_invoiced = 0;
            if (!empty($invoice_history)) {
                foreach ($invoice_history as $invoice) {
                    $total_invoiced += floatval($invoice['amount']);
                }
            }

            // 计算剩余可开票金额（用于日志记录）
            $remaining_amount = floatval($orderData['invoice_amount']) - $total_invoiced;
            \Think\Log::record('剩余可开票金额: ' . $remaining_amount . ' (invoice_amount=' . $orderData['invoice_amount'] . ', total_invoiced=' . $total_invoiced . ')', 'INFO');

            // 更新数据库
            $model_pay->where(['orderid' => $order_id])->save($updateData);

            \Think\Log::record('订单核验状态已保存到数据库: ' . $order_id . ', 核验时间: ' . date('Y-m-d H:i:s', time()), 'INFO');

            // 记录成功日志
            \Think\Log::record('订单核验成功: ' . $order_id . ', 可开票金额: ' . $orderData['invoice_amount'], 'INFO');

            // 准备成功结果
            // 获取订单金额信息
            $total = floatval($orderData['total']);
            $refund_amount = floatval($orderData['refund_amount']);

            // 计算已开票金额 - 使用实际已开票金额，而不是通过计算得出
            // 从数据库中获取已开票金额，排除冲红（作废）或失败的发票
            $total_invoiced = 0;
            if (!empty($invoice_history)) {
                foreach ($invoice_history as $invoice) {
                    // 排除冲红（作废）或失败的发票
                    if (
                        $invoice['status'] != \Common\Lib\InvoiceStatusConstants::INVOICE_FAILED &&
                        $invoice['status'] != \Common\Lib\InvoiceStatusConstants::INVOICE_VOIDED
                    ) {
                        $total_invoiced += floatval($invoice['amount']);
                    }
                }
            }

            // 格式化已开票金额
            $invoiced_amount = number_format($total_invoiced, 2, '.', '');

            // 计算实际可开票金额 = 订单总金额 - 退款金额 - 已开票金额
            $actual_remaining = $total - $refund_amount - $total_invoiced;
            // 确保不为负数
            $actual_remaining = max(0, $actual_remaining);
            $actual_remaining = number_format($actual_remaining, 2, '.', '');

            // 记录计算结果
            \Think\Log::record('订单核验金额计算: 订单总金额=' . $total .
                ', 退款金额=' . $refund_amount .
                ', 已开票金额=' . $invoiced_amount .
                ', 实际可开票金额=' . $actual_remaining, 'INFO');

            $verification_result = [
                'success' => true,
                'class' => 'success',
                'title' => '订单核验成功',
                'message' => '该订单可以申请发票，请点击下方按钮继续',
                'order_id' => $order_id,
                'total_amount' => $orderData['total'],
                'refund_amount' => $orderData['refund_amount'],
                'invoiced_amount' => $invoiced_amount,
                'remaining_amount' => $actual_remaining
            ];

            $this->assign('verification_result', $verification_result);
            $this->assign('invoice_css', true);
            $this->assign('order_id', $order_id); // 保持订单号在表单中显示
            $this->assign('invoice_history', $invoice_history); // 传递发票历史记录
            $this->display('verifyOrder');
        } catch (\Exception $e) {
            // 记录异常日志
            \Think\Log::record('订单核验异常: ' . $e->getMessage(), 'ERROR');

            // 准备错误结果
            $verification_result = [
                'success' => false,
                'class' => 'error',
                'title' => '系统错误',
                'message' => '订单核验过程中发生错误: ' . $e->getMessage(),
                'order_id' => I('post.order_id')
            ];

            $this->assign('verification_result', $verification_result);
            $this->assign('invoice_css', true);
            $this->assign('order_id', I('post.order_id')); // 保持订单号在表单中显示
            $this->display('verifyOrder');
        }
    }

    /**
     * 在线支付申请发票通道
     * 需要先通过订单核验才能访问
     */
    public function onlineInvoice()
    {
        try {
            // 获取订单号
            $post['order_id'] = I('get.order_id');
            $order_id = $post['order_id'];

            // 从数据库检查订单是否已经通过核验
            $model_pay = M('subPay');
            $db_pay_info = $model_pay->where(['orderid' => $order_id])->find();

            if (!$db_pay_info) {
                // 订单不存在于数据库中
                \Think\Log::record('订单不存在于数据库中: ' . $order_id, 'WARN');
                $this->redirect('verifyOrder', ['order_id' => $order_id]);
                return;
            }

            // 检查是否已核验
            if (!isset($db_pay_info['is_check']) || $db_pay_info['is_check'] != 1) {
                // 订单未通过核验，重定向到核验页面
                \Think\Log::record('尝试访问未核验的订单发票申请: ' . $order_id, 'WARN');
                $this->redirect('verifyOrder', ['order_id' => $order_id]);
                return;
            }

            // 检查核验时间是否已过期（24小时有效期）
            $verifyTime = $db_pay_info['check_time'];
            if (!$verifyTime || (time() - $verifyTime > 86400)) { // 24小时 = 86400秒
                // 核验已过期，重置核验状态并重定向到核验页面
                $model_pay->where(['orderid' => $order_id])->save(['is_check' => 0, 'check_time' => 0]);
                \Think\Log::record('订单核验已过期: ' . $order_id, 'INFO');
                $this->redirect('verifyOrder', ['order_id' => $order_id]);
                return;
            }

            // 获取发票历史记录
            $invoice_model = M('OaInvoice');
            $invoice_history = $invoice_model->where(['order_id' => $order_id])->select();

            // 计算已开票金额
            $total_invoiced = 0;
            if (!empty($invoice_history)) {
                foreach ($invoice_history as $invoice) {
                    $total_invoiced += floatval($invoice['amount']);
                }
            }

            // 使用支付查询服务获取订单详情
            try {
                $complete_order_info = $this->paymentSyncService->getCompleteOrderInfo($order_id, false);
                \Think\Log::record('订单信息获取成功: ' . json_encode($complete_order_info['pay_info'], JSON_UNESCAPED_UNICODE), 'DEBUG');

                // 合并完整订单信息到invoice_data
                $invoice_data = $complete_order_info;
            } catch (\Exception $e) {
                \Think\Log::record('订单信息获取失败，回退到基本检查: ' . $e->getMessage(), 'WARN');
                // 如果同步失败，回退到基本检查
                $invoice_data = $this->invoiceService->isOnlineOrderCanInvoice($post);
            }

            // 更新发票历史记录和已开票金额
            $invoice_data['invoice_history'] = $invoice_history;
            $invoice_data['total_invoiced'] = $total_invoiced;

            // 检查是否是全额退款 - 多重检查
            // 1. 检查is_full_refund标记
            if (isset($invoice_data['pay_info']['is_full_refund']) && $invoice_data['pay_info']['is_full_refund']) {
                \Think\Log::record('订单全额退款检查(标记): ' . $order_id . ' 被标记为全额退款', 'INFO');
                $this->error('该订单已全额退款，无法开具发票');
            }

            // 2. 检查退款金额是否接近或等于订单总金额
            $total = floatval($invoice_data['pay_info']['total']);
            $refund_total = isset($invoice_data['pay_info']['refund_total']) ? floatval($invoice_data['pay_info']['refund_total']) : 0;

            if ($refund_total >= $total * 0.999) {
                \Think\Log::record('订单全额退款检查(金额): ' . $order_id . ', 订单总额=' . $total . ', 退款金额=' . $refund_total, 'INFO');

                // 更新数据库中的标记
                $model_pay = M('SubPay');
                $updateData = [
                    'is_refund' => 1,
                    'refund_total' => number_format($total, 2, '.', ''),
                    'invoice_total' => '0.00',
                    'refund_count' => isset($invoice_data['pay_info']['refund_count']) && is_numeric($invoice_data['pay_info']['refund_count']) ?
                        $invoice_data['pay_info']['refund_count'] : 1
                ];

                $model_pay->where(['orderid' => $order_id])->save($updateData);
                \Think\Log::record('订单全额退款标记已更新: ' . $order_id, 'INFO');

                $this->error('该订单已全额退款，无法开具发票');
            }

            // 3. 检查invoice_total是否为0
            if (isset($invoice_data['pay_info']['invoice_total']) && floatval($invoice_data['pay_info']['invoice_total']) <= 0) {
                // 如果invoice_total为0，但订单总金额大于0且没有退款记录，可能是invoice_total字段未被正确设置
                // 在这种情况下，尝试重新计算并更新invoice_total
                $total = floatval($invoice_data['pay_info']['total']);
                $refund_total = isset($invoice_data['pay_info']['refund_total']) ? floatval($invoice_data['pay_info']['refund_total']) : 0;
                $is_refund = isset($invoice_data['pay_info']['is_refund']) ? $invoice_data['pay_info']['is_refund'] : 0;

                if ($total > 0 && ($refund_total <= 0 || $refund_total < $total * 0.999)) {
                    // 计算可开票金额 = 订单总金额 - 退款金额
                    $invoice_total = $total - $refund_total;

                    if ($invoice_total > 0) {
                        // 更新数据库
                        $model_pay = M('SubPay');
                        $updateData = [
                            'invoice_total' => number_format($invoice_total, 2, '.', '')
                        ];

                        if ($is_refund == 1) {
                            $updateData['is_refund'] = 1;
                            $updateData['refund_total'] = number_format($refund_total, 2, '.', '');
                        }

                        $model_pay->where(['orderid' => $order_id])->save($updateData);

                        \Think\Log::record('修复订单可开票金额: ' . $order_id . ', 订单总额=' . $total .
                            ', 退款金额=' . $refund_total . ', 可开票金额=' . $invoice_total, 'INFO');

                        // 更新内存中的数据
                        $invoice_data['pay_info']['invoice_total'] = number_format($invoice_total, 2, '.', '');
                        $invoice_data['remaining_amount'] = number_format($invoice_total - floatval($invoice_data['total_invoiced']), 2, '.', '');
                    } else {
                        \Think\Log::record('订单可开票金额计算为0: ' . $order_id, 'INFO');
                        $this->error('该订单无可开票金额');
                    }
                } else {
                    \Think\Log::record('订单可开票金额为0: ' . $order_id, 'INFO');
                    $this->error('该订单无可开票金额');
                }
            }

            // 4. 检查可开票金额
            if (floatval($invoice_data['remaining_amount']) <= 0) {
                \Think\Log::record('订单剩余可开票金额为0: ' . $order_id, 'INFO');
                $this->error('订单金额已经达到最大开票金额或已全额退款');
            }

            // 注意：金额已经在服务类中格式化，这里不需要再次格式化
            // 但为了确保前端显示一致性，我们保留这些代码
            $invoice_data['pay_info']['total'] = number_format(floatval($invoice_data['pay_info']['total']), 2, '.', '');
            $invoice_data['total_invoiced'] = number_format(floatval($invoice_data['total_invoiced']), 2, '.', '');
            $invoice_data['remaining_amount'] = number_format(floatval($invoice_data['remaining_amount']), 2, '.', '');

            // 获取发票策略信息
            $maxInvoiceTimes = \Common\Lib\InvoiceEligibilityConfig::getMaxInvoiceTimes();
            $allowPartialInvoice = \Common\Lib\InvoiceEligibilityConfig::isPartialInvoiceAllowed();

            // 准备发票策略提示信息
            $invoiceStrategyInfo = [];

            // 判断是否可以开多张发票
            if ($maxInvoiceTimes === null) {
                $invoiceStrategyInfo['multiple_invoices'] = true;
                $invoiceStrategyInfo['max_times'] = '\u4e0d\u9650\u5236';
            } else {
                $invoiceStrategyInfo['multiple_invoices'] = $maxInvoiceTimes > 1;
                $invoiceStrategyInfo['max_times'] = $maxInvoiceTimes;
            }

            // 判断是否可以部分金额开票
            $invoiceStrategyInfo['partial_invoice'] = $allowPartialInvoice;

            // 判断已申请次数
            $invoiceStrategyInfo['applied_times'] = count($invoice_data['invoice_history']);

            // 判断是否还可以继续申请 - 只考虑最大申请次数，其他条件由前端处理
            // 注意：前端的 checkCanApplyMore 函数会综合考虑剩余金额、是否允许多张发票和最大申请次数
            $invoiceStrategyInfo['can_apply_more'] = $maxInvoiceTimes === null || $invoiceStrategyInfo['applied_times'] < $maxInvoiceTimes;

            // 获取用户信息
            $userId = str_auth(cookie('userid'), 'DECODE');
            $userInfo = [];
            if (!empty($userId)) {
                $userModel = M('Member');
                $userInfo = $userModel->where(['id' => $userId])->find();

                // 获取用户最近的发票信息
                $invoiceModel = M('OaInvoice');
                $lastInvoice = $invoiceModel->where(['user_id' => $userId])
                    ->order('id desc')
                    ->find();

                if (!empty($lastInvoice)) {
                    // 使用最近的发票信息填充用户信息
                    $userInfo['invoice_title'] = $lastInvoice['invoice_title'];
                    $userInfo['buyer_tax_num'] = $lastInvoice['buyer_tax_num'];
                    $userInfo['buyer_phone'] = $lastInvoice['buyer_phone'];
                }
            }

            // 处理支付状态和支付方式
            $paymentStatus = [];
            if (isset($invoice_data['pay_info']['status'])) {
                $status = intval($invoice_data['pay_info']['status']);
                $paymentStatus['code'] = $status;
                $paymentStatus['text'] = OnlinePaymentStatusConstants::getStatusText($status);
                $paymentStatus['class'] = OnlinePaymentStatusConstants::getStatusClass($status);
                $paymentStatus['is_success'] = OnlinePaymentStatusConstants::isSuccess($status);
            }

            // 处理支付方式
            $paymentMethod = [];
            if (isset($invoice_data['pay_info']['paytype'])) {
                $paytype = $invoice_data['pay_info']['paytype'];
                $paymentMethod['code'] = $paytype;

                // 根据不同的支付方式设置显示文本
                switch ($paytype) {
                    case 'bank':
                        $paymentMethod['text'] = '银行转账';
                        break;
                    case 'alipay':
                        $paymentMethod['text'] = '支付宝';
                        break;
                    case 'wechat':
                        $paymentMethod['text'] = '微信支付';
                        break;
                    case 'paypal':
                        $paymentMethod['text'] = 'PayPal';
                        break;
                    default:
                        $paymentMethod['text'] = $paytype;
                }
            }

            // 获取发票项目选项 (排除版面费)
            $invoiceItemOptions = $this->getInvoiceItemService()->getFrontendInvoiceItemOptions();

            // 调试信息
            \Think\Log::record('发票项目选项: ' . json_encode($invoiceItemOptions, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 记录调试信息，查看退款相关字段
            \Think\Log::record('退款相关字段调试: order_id=' . $order_id .
                ', is_refund=' . (isset($invoice_data['pay_info']['is_refund']) ? $invoice_data['pay_info']['is_refund'] : 'null') .
                ', refund_total=' . (isset($invoice_data['pay_info']['refund_total']) ? $invoice_data['pay_info']['refund_total'] : 'null'), 'DEBUG');

            // 确保退款字段存在且正确
            if (isset($invoice_data['pay_info']['is_refund']) && $invoice_data['pay_info']['is_refund'] == 1) {
                // 如果是退款但refund_total不存在或为0，从数据库重新获取
                if (!isset($invoice_data['pay_info']['refund_total']) || floatval($invoice_data['pay_info']['refund_total']) <= 0) {
                    $model_pay = M('subPay');
                    $db_pay_info = $model_pay->where(['orderid' => $order_id])->find();
                    if ($db_pay_info && isset($db_pay_info['refund_total']) && floatval($db_pay_info['refund_total']) > 0) {
                        $invoice_data['pay_info']['refund_total'] = $db_pay_info['refund_total'];
                        \Think\Log::record('从数据库更新退款金额: ' . $db_pay_info['refund_total'], 'INFO');
                    }
                }
            }

            // 将所有数据传递给视图
            $this->assign('pay_info', $invoice_data['pay_info']);
            $this->assign('invoice_history', $invoice_data['invoice_history']);
            $this->assign('total_invoiced', $invoice_data['total_invoiced']);
            $this->assign('remaining_amount', $invoice_data['remaining_amount']);
            $this->assign('reg_id', $invoice_data['reg_id']); // 增加 reg_id 信息
            $this->assign('invoice_strategy', $invoiceStrategyInfo);
            $this->assign('user_info', $userInfo); // 增加用户信息
            $this->assign('payment_status', $paymentStatus); // 增加支付状态信息
            $this->assign('payment_method', $paymentMethod); // 增加支付方式信息
            $this->assign('invoice_item_options', $invoiceItemOptions); // 增加发票项目选项

            $this->display('onlineInvoice');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }
    /**
     * 线下转账发票申请入口
     * 暂未实现，重定向到发票列表页面
     */
    public function offline()
    {
        // 重定向到发票列表页面
        $this->redirect('BillList');
    }
    /**
     * 保存发票申请信息
     */
    public function save()
    {
        try {
            // 记录开始处理请求
            \Think\Log::record('开始处理发票申请保存请求', 'DEBUG');

            // 检查用户权限
            $userId = str_auth(cookie('userid'), 'DECODE');
            if (empty($userId)) {
                \Think\Log::record('用户未登录，无法保存发票申请', 'WARN');
                $this->error('您的登录已过期，请重新登录');
                return;
            }

            \Think\Log::record('用户ID: ' . $userId, 'DEBUG');

            $post = I('post.');
            // 记录原始POST数据
            \Think\Log::record('原始POST数据: ' . json_encode($post, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 增强输入过滤
            $post['invoice_title'] = htmlspecialchars(trim($post['invoice_title']));
            $post['buyer_tax_num'] = preg_replace('/[^0-9A-Z]/', '', $post['buyer_tax_num']);
            $post['goods_info'] = htmlspecialchars(trim($post['goods_info']));
            $post['buyer_email'] = filter_var(trim($post['buyer_email']), FILTER_SANITIZE_EMAIL);
            $post['user_id'] = $userId; // 添加用户ID

            // 处理新增字段
            if (isset($post['buyer_tel'])) {
                $post['buyer_tel'] = htmlspecialchars(trim($post['buyer_tel']));
            }
            if (isset($post['remark'])) {
                $post['remark'] = htmlspecialchars(trim($post['remark']));
            }

            // 记录过滤后的数据
            \Think\Log::record('过滤后的数据: ' . json_encode($post, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 检查必填字段
            $required_fields = [
                'order_id' => '订单号',
                'invoice_title' => '发票抬头',
                'buyer_tax_num' => '税号',
                'amount' => '开票金额',
                'goods_info' => '开票项目',
                'buyer_email' => '买方邮箱',
                'invoice_type' => '发票类型'
            ];

            foreach ($required_fields as $field => $name) {
                if (empty($post[$field])) {
                    \Think\Log::record('缺少必填字段: ' . $name, 'WARN');
                    $this->error('缺少必填字段: ' . $name);
                    return;
                }
            }

            // 使用模型处理发票申请
            $model = D('MemberOaInvoice');
            \Think\Log::record('开始调用 addNewOnlinePay 方法', 'DEBUG');
            $result = $model->addNewOnlinePay($post);
            \Think\Log::record('addNewOnlinePay 方法返回结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 处理返回结果
            if ($result['status'] == 1) {
                // 注意：我们不再需要在这里调用状态同步服务，因为在 MemberOaInvoiceModel::addNewOnlinePay 方法中
                // 已经通过 AuditInvoiceRelationService 自动关联了查账记录并更新了状态

                // 记录日志
                \Think\Log::record('在线发票申请成功，ID: ' . $result['id'], 'INFO');

                // 成功提交
                $this->redirect('successful', ['id' => $result['id']]);
            } else {
                // 提交失败
                \Think\Log::record('发票申请失败: ' . $result['info'], 'ERROR');
                $this->error($result['info']);
            }
        } catch (\Exception $e) {
            // 异常处理
            \Think\Log::record('发票申请异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'ERROR');
            $this->error('发票申请失败: ' . $e->getMessage());
        }
    }

    /**
     * AJAX方式保存发票申请信息
     * 返回JSON格式的响应，而不是重定向
     */
    public function saveAjax()
    {
        // 记录开始处理AJAX请求
        \Think\Log::record('开始处理发票申请AJAX保存请求', 'DEBUG');

        // 禁止直接访问该方法
        if (!IS_AJAX) {
            \Think\Log::record('非AJAX请求尝试访问saveAjax方法', 'WARN');
            $this->error('非法请求');
            return;
        }

        // 检查用户权限
        $userId = str_auth(cookie('userid'), 'DECODE');
        if (empty($userId)) {
            \Think\Log::record('AJAX请求：用户未登录，无法保存发票申请', 'WARN');
            $this->ajaxReturn(['status' => 0, 'message' => '您的登录已过期，请重新登录']);
            return;
        }

        \Think\Log::record('AJAX请求：用户ID: ' . $userId, 'DEBUG');

        try {
            $post = I('post.');
            // 记录原始POST数据
            \Think\Log::record('AJAX请求：原始POST数据: ' . json_encode($post, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 增强输入过滤
            $post['invoice_title'] = htmlspecialchars(trim($post['invoice_title']));
            $post['buyer_tax_num'] = preg_replace('/[^0-9A-Z]/', '', $post['buyer_tax_num']);
            $post['goods_info'] = htmlspecialchars(trim($post['goods_info']));
            $post['buyer_email'] = filter_var(trim($post['buyer_email']), FILTER_SANITIZE_EMAIL);
            $post['user_id'] = $userId; // 添加用户ID

            // 处理新增字段
            if (isset($post['buyer_tel'])) {
                $post['buyer_tel'] = htmlspecialchars(trim($post['buyer_tel']));
            }
            if (isset($post['remark'])) {
                $post['remark'] = htmlspecialchars(trim($post['remark']));
            }

            // 记录过滤后的数据
            \Think\Log::record('AJAX请求：过滤后的数据: ' . json_encode($post, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 检查必填字段
            $required_fields = [
                'order_id' => '订单号',
                'invoice_title' => '发票抬头',
                'buyer_tax_num' => '税号',
                'amount' => '开票金额',
                'goods_info' => '开票项目',
                'buyer_email' => '买方邮箱',
                'invoice_type' => '发票类型'
            ];

            foreach ($required_fields as $field => $name) {
                if (empty($post[$field])) {
                    \Think\Log::record('AJAX请求：缺少必填字段: ' . $name, 'WARN');
                    $this->ajaxReturn(['status' => 0, 'message' => '缺少必填字段: ' . $name]);
                    return;
                }
            }

            // 使用模型处理发票申请
            $model = D('MemberOaInvoice');
            \Think\Log::record('AJAX请求：开始调用 addNewOnlinePay 方法', 'DEBUG');
            $result = $model->addNewOnlinePay($post);
            \Think\Log::record('AJAX请求：addNewOnlinePay 方法返回结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 处理返回结果
            if ($result['status'] == 1) {
                // 注意：我们不再需要在这里调用状态同步服务，因为在 MemberOaInvoiceModel::addNewOnlinePay 方法中
                // 已经通过 AuditInvoiceRelationService 自动关联了查账记录并更新了状态

                // 记录日志
                \Think\Log::record('在线发票申请成功（AJAX），ID: ' . $result['id'], 'INFO');

                // 获取更新后的订单信息
                $post['order_id'] = $post['order_id'];
                try {
                    // 尝试获取订单信息
                    $invoice_data = $this->invoiceService->isOnlineOrderCanInvoice(['order_id' => $post['order_id']]);
                } catch (\Exception $e) {
                    // 如果是因为剩余金额为0导致的异常，则手动构造数据
                    if (strpos($e->getMessage(), '该订单已无剩余可开票金额') !== false) {
                        // 获取订单信息
                        $model_pay = M('subPay');
                        $pay_info = $model_pay->where(['orderid' => $post['order_id']])->find();

                        // 获取发票历史记录
                        $invoice_model = M('OaInvoice');
                        $invoice_history = $invoice_model->where(['order_id' => $post['order_id']])->select();

                        // 计算已开票金额
                        $total_invoiced = 0;
                        foreach ($invoice_history as $invoice) {
                            $total_invoiced += floatval($invoice['amount']);
                        }

                        // 检查是否有退款信息
                        $refund_total = isset($pay_info['refund_total']) ? floatval($pay_info['refund_total']) : 0;
                        $is_refund = isset($pay_info['is_refund']) ? $pay_info['is_refund'] : 0;

                        // 记录退款信息
                        \Think\Log::record('订单退款信息: is_refund=' . $is_refund .
                            ', refund_total=' . $refund_total, 'INFO');

                        // 计算可开票金额 = 订单总金额 - 退款金额
                        $invoice_total = floatval($pay_info['total']) - $refund_total;

                        // 确保可开票金额不为负数
                        if ($invoice_total < 0) {
                            $invoice_total = 0;
                        }

                        // 计算剩余可开票金额 = 可开票金额 - 已开票金额
                        $remaining_amount = $invoice_total - $total_invoiced;

                        // 确保剩余可开票金额不为负数
                        if ($remaining_amount < 0) {
                            $remaining_amount = 0;
                        }

                        // 构造数据
                        $invoice_data = [
                            'pay_info' => $pay_info,
                            'invoice_history' => $invoice_history,
                            'total_invoiced' => $total_invoiced,
                            'remaining_amount' => $remaining_amount
                        ];

                        // 记录构造的数据
                        \Think\Log::record('手动构造的发票数据: total=' . $pay_info['total'] .
                            ', refund_total=' . $refund_total .
                            ', invoice_total=' . $invoice_total .
                            ', total_invoiced=' . $total_invoiced .
                            ', remaining_amount=' . $remaining_amount, 'INFO');
                    } else {
                        // 如果是其他异常，则抛出
                        throw $e;
                    }
                }

                // 格式化金额
                $invoice_data['pay_info']['total'] = number_format(floatval($invoice_data['pay_info']['total']), 2, '.', '');
                $invoice_data['total_invoiced'] = number_format(floatval($invoice_data['total_invoiced']), 2, '.', '');
                $invoice_data['remaining_amount'] = number_format(floatval($invoice_data['remaining_amount']), 2, '.', '');

                // 返回成功响应
                $response = [
                    'status' => 1,
                    'message' => '发票申请提交成功',
                    'invoice_id' => $result['id'],
                    'order_data' => [
                        'pay_info' => $invoice_data['pay_info'],
                        'invoice_history' => $invoice_data['invoice_history'],
                        'total_invoiced' => $invoice_data['total_invoiced'],
                        'remaining_amount' => $invoice_data['remaining_amount']
                    ]
                ];

                $this->ajaxReturn($response);
            } else {
                // 返回失败响应
                $this->ajaxReturn(['status' => 0, 'message' => $result['info']]);
            }
        } catch (\Exception $e) {
            // 异常处理
            $this->ajaxReturn(['status' => 0, 'message' => '发票申请失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 游客发票申请入口页面
     * 显示订单号输入表单，重定向到核验页面
     */
    public function guestInvoiceEntry()
    {
        $this->redirect('verifyOrderGuest');
    }

    /**
     * 游客订单核验页面
     * 显示订单核验表单，用户需要先通过核验才能进入发票申请页面
     */
    public function verifyOrderGuest()
    {
        // 获取订单号
        $order_id = I('get.order_id');

        // 将订单号传递给视图
        $this->assign('order_id', $order_id);

        // 启用发票样式
        $this->assign('invoice_css', true);

        // 显示核验页面
        $this->display();
    }

    /**
     * 处理游客订单核验表单提交
     * 验证订单是否可以开票，并将结果存储在session中
     */
    public function verifyOrderGuestSubmit()
    {
        try {
            // 获取订单号
            $order_id = I('post.order_id');

            if (empty($order_id)) {
                $this->error('Please enter an order number');
                return;
            }

            // 记录调试信息
            \Think\Log::record('开始核验游客订单: ' . $order_id, 'INFO');

            // 使用支付查询服务获取订单详情
            $queryService = new \Common\Service\PaymentQueryService();
            $result = $queryService->queryOrderDetail($order_id);

            // 如果查询失败
            if (!$result['success']) {
                \Think\Log::record('游客订单核验失败: ' . $result['message'], 'WARN');

                // 准备错误结果
                $verification_result = [
                    'success' => false,
                    'class' => 'error',
                    'title' => 'Verification Failed',
                    'message' => $result['message'],
                    'order_id' => $order_id
                ];

                $this->assign('verification_result', $verification_result);
                $this->assign('invoice_css', true);
                $this->assign('order_id', $order_id); // 保持订单号在表单中显示
                $this->display('verifyOrderGuest');
                return;
            }

            // 获取订单信息
            $orderData = $result['data'];

            // 检查订单是否可以开票
            if (!$orderData['can_invoice']) {
                \Think\Log::record('游客订单不可开票: ' . $order_id . ', 原因: ' . $orderData['invoice_reason'], 'WARN');

                // 检查是否是全额退款 - 多种判断方式
                $is_full_refund = false;

                // 1. 通过金额比较判断
                if (isset($orderData['refund_amount']) && isset($orderData['total'])) {
                    $refund_amount = floatval($orderData['refund_amount']);
                    $total_amount = floatval($orderData['total']);
                    $is_full_refund = ($refund_amount >= $total_amount * 0.999);
                }

                // 2. 通过invoice_reason判断
                $refund_keywords = ['fully refunded', 'full refund', 'has been fully refunded'];
                foreach ($refund_keywords as $keyword) {
                    if (stripos($orderData['invoice_reason'], $keyword) !== false) {
                        $is_full_refund = true;
                        break;
                    }
                }

                // 3. 通过invoice_amount判断
                if (isset($orderData['invoice_amount']) && floatval($orderData['invoice_amount']) <= 0) {
                    $is_full_refund = true;
                }

                // 记录全额退款判断的详细日志
                \Think\Log::record('游客订单全额退款判断: ' . $order_id .
                    ', 通过金额比较=' . (isset($orderData['refund_amount']) && isset($orderData['total']) && floatval($orderData['refund_amount']) >= floatval($orderData['total']) * 0.999 ? 'true' : 'false') .
                    ', 通过关键词=' . (stripos($orderData['invoice_reason'], 'fully refunded') !== false ? 'true' : 'false') .
                    ', 通过invoice_amount=' . (isset($orderData['invoice_amount']) && floatval($orderData['invoice_amount']) <= 0 ? 'true' : 'false') .
                    ', 最终判断=' . ($is_full_refund ? 'true' : 'false'), 'INFO');

                // 如果是全额退款，使用特殊的显示样式
                if ($is_full_refund) {
                    // 获取订单金额信息
                    $total = isset($orderData['total']) ? floatval($orderData['total']) : 0;
                    $refund_amount = isset($orderData['refund_amount']) ? floatval($orderData['refund_amount']) : 0;

                    // 准备全额退款结果，包含金额信息
                    $verification_result = [
                        'success' => false,
                        'class' => 'danger',
                        'title' => 'Order Fully Refunded',
                        'message' => 'This order has been fully refunded and cannot be invoiced',
                        'order_id' => $order_id,
                        'is_full_refund' => true,
                        'total_amount' => number_format($total, 2, '.', ''),
                        'refund_amount' => number_format($refund_amount, 2, '.', ''),
                        'invoiced_amount' => '0.00',
                        'remaining_amount' => '0.00'
                    ];

                    // 记录使用全额退款显示样式
                    \Think\Log::record('使用游客订单全额退款显示样式: ' . $order_id . ', 订单总额=' . $total . ', 退款金额=' . $refund_amount, 'INFO');
                } else {
                    // 准备普通警告结果
                    $verification_result = [
                        'success' => false,
                        'class' => 'warning',
                        'title' => 'Cannot Issue Invoice',
                        'message' => $orderData['invoice_reason'],
                        'order_id' => $order_id
                    ];
                }

                $this->assign('verification_result', $verification_result);
                $this->assign('invoice_css', true);
                $this->assign('order_id', $order_id); // 保持订单号在表单中显示
                $this->display('verifyOrderGuest');
                return;
            }

            // 检查货币类型是否允许开票
            if (!isset($orderData['currency']) || !\Common\Lib\InvoiceEligibilityConfig::isCurrencyAllowed($orderData['currency'] === 'CNY' ? 0 : 1)) {
                \Think\Log::record('游客订单货币类型不支持开票: ' . $order_id . ', 货币类型: ' . $orderData['currency'], 'WARN');

                // 准备警告结果
                $verification_result = [
                    'success' => false,
                    'class' => 'warning',
                    'title' => 'Currency Not Supported',
                    'message' => 'Sorry, only CNY orders are eligible for invoice application.',
                    'order_id' => $order_id
                ];

                $this->assign('verification_result', $verification_result);
                $this->assign('invoice_css', true);
                $this->assign('order_id', $order_id); // 保持订单号在表单中显示
                $this->display('verifyOrderGuest');
                return;
            }

            // 订单核验成功，将订单标记为已核验（保存到数据库）
            $model_pay = M('SubPay');  // 使用与 PaymentQueryService 相同的表名大小写
            $db_pay_info = $model_pay->where(['orderid' => $order_id])->find();

            if (!$db_pay_info) {
                \Think\Log::record('游客订单不存在于数据库中: ' . $order_id . ', SQL: ' . $model_pay->getLastSql(), 'ERROR');
                throw new \Exception('Order not found in database');
            }

            // 更新数据库中的核验状态
            $updateData = [
                'is_check' => 1,
                'check_time' => time()
            ];

            $model_pay->where(['orderid' => $order_id])->save($updateData);

            \Think\Log::record('游客订单核验状态已保存到数据库: ' . $order_id . ', 核验时间: ' . date('Y-m-d H:i:s', time()), 'INFO');

            // 记录成功日志
            \Think\Log::record('游客订单核验成功: ' . $order_id . ', 可开票金额: ' . $orderData['invoice_amount'], 'INFO');

            // 准备成功结果
            // 计算已开票金额：订单总金额 - 退款金额 - 可开票金额
            $total = floatval($orderData['total']);
            $refund_amount = floatval($orderData['refund_amount']);
            $invoice_amount = floatval($orderData['invoice_amount']);

            // 计算已开票金额
            $invoiced_amount = $total - $refund_amount - $invoice_amount;
            // 确保不为负数
            $invoiced_amount = max(0, $invoiced_amount);

            $verification_result = [
                'success' => true,
                'class' => 'success',
                'title' => 'Verification Successful',
                'message' => 'This order is eligible for invoice application. Please click the button below to continue.',
                'order_id' => $order_id,
                'total_amount' => $orderData['total'],
                'refund_amount' => $orderData['refund_amount'],
                'invoiced_amount' => number_format($invoiced_amount, 2, '.', ''),
                'remaining_amount' => $orderData['invoice_amount']
            ];

            $this->assign('verification_result', $verification_result);
            $this->assign('invoice_css', true);
            $this->assign('order_id', $order_id); // 保持订单号在表单中显示
            $this->display('verifyOrderGuest');
        } catch (\Exception $e) {
            // 记录异常日志
            \Think\Log::record('游客订单核验异常: ' . $e->getMessage(), 'ERROR');

            // 准备错误结果
            $verification_result = [
                'success' => false,
                'class' => 'error',
                'title' => 'System Error',
                'message' => 'An error occurred during order verification: ' . $e->getMessage(),
                'order_id' => I('post.order_id')
            ];

            $this->assign('verification_result', $verification_result);
            $this->assign('invoice_css', true);
            $this->assign('order_id', I('post.order_id')); // 保持订单号在表单中显示
            $this->display('verifyOrderGuest');
        }
    }

    /**
     * 游客申请发票通道
     * 适用于未登录情况下的付款记录（userid为空）
     * 需要先通过订单核验才能访问
     */
    public function guestInvoice()
    {
        try {
            $post['order_id'] = I('get.order_id');
            $order_id = $post['order_id'];

            // 从数据库检查订单是否已经通过核验
            $model_pay = M('SubPay');  // 使用与 PaymentQueryService 相同的表名大小写
            $db_pay_info = $model_pay->where(['orderid' => $order_id])->find();

            if (!$db_pay_info) {
                // 订单不存在于数据库中
                \Think\Log::record('游客订单不存在于数据库中: ' . $order_id, 'WARN');
                $this->redirect('verifyOrderGuest', ['order_id' => $order_id]);
                return;
            }

            // 检查是否已核验
            if (!isset($db_pay_info['is_check']) || $db_pay_info['is_check'] != 1) {
                // 订单未通过核验，重定向到核验页面
                \Think\Log::record('游客尝试访问未核验的订单发票申请: ' . $order_id, 'WARN');
                $this->redirect('verifyOrderGuest', ['order_id' => $order_id]);
                return;
            }

            // 检查核验时间是否已过期（24小时有效期）
            $verifyTime = $db_pay_info['check_time'];
            if (!$verifyTime || (time() - $verifyTime > 86400)) { // 24小时 = 86400秒
                // 核验已过期，重置核验状态并重定向到核验页面
                $model_pay->where(['orderid' => $order_id])->save(['is_check' => 0, 'check_time' => 0]);
                \Think\Log::record('游客订单核验已过期: ' . $order_id, 'INFO');
                $this->redirect('verifyOrderGuest', ['order_id' => $order_id]);
                return;
            }

            // 使用支付同步服务获取完整的订单信息，包括同步最新状态和计算退款金额
            try {
                $complete_order_info = $this->paymentSyncService->getCompleteOrderInfo($order_id, true);
                \Think\Log::record('游客订单信息获取成功: ' . json_encode($complete_order_info['pay_info'], JSON_UNESCAPED_UNICODE), 'DEBUG');

                // 合并完整订单信息到invoice_data
                $invoice_data = $complete_order_info;
            } catch (\Exception $e) {
                \Think\Log::record('游客订单信息获取失败，回退到基本检查: ' . $e->getMessage(), 'WARN');
                // 如果同步失败，回退到基本检查
                $invoice_data = $this->invoiceService->isGuestOrderCanInvoice($post);
            }

            // 检查是否是全额退款 - 多重检查
            // 1. 检查is_full_refund标记
            if (isset($invoice_data['pay_info']['is_full_refund']) && $invoice_data['pay_info']['is_full_refund']) {
                \Think\Log::record('游客订单全额退款检查(标记): ' . $order_id . ' 被标记为全额退款', 'INFO');
                $this->error('This order has been fully refunded and cannot be invoiced');
            }

            // 2. 检查退款金额是否接近或等于订单总金额
            $total = floatval($invoice_data['pay_info']['total']);
            $refund_total = isset($invoice_data['pay_info']['refund_total']) ? floatval($invoice_data['pay_info']['refund_total']) : 0;

            if ($refund_total >= $total * 0.999) {
                \Think\Log::record('游客订单全额退款检查(金额): ' . $order_id . ', 订单总额=' . $total . ', 退款金额=' . $refund_total, 'INFO');

                // 更新数据库中的标记
                $model_pay = M('SubPay');
                $updateData = [
                    'is_refund' => 1,
                    'refund_total' => number_format($total, 2, '.', ''),
                    'invoice_total' => '0.00',
                    'refund_count' => isset($invoice_data['pay_info']['refund_count']) && is_numeric($invoice_data['pay_info']['refund_count']) ?
                        $invoice_data['pay_info']['refund_count'] : 1
                ];

                $model_pay->where(['orderid' => $order_id])->save($updateData);
                \Think\Log::record('游客订单全额退款标记已更新: ' . $order_id, 'INFO');

                $this->error('This order has been fully refunded and cannot be invoiced');
            }

            // 3. 检查invoice_total是否为0
            if (isset($invoice_data['pay_info']['invoice_total']) && floatval($invoice_data['pay_info']['invoice_total']) <= 0) {
                // 如果invoice_total为0，但订单总金额大于0且没有退款记录，可能是invoice_total字段未被正确设置
                // 在这种情况下，尝试重新计算并更新invoice_total
                $total = floatval($invoice_data['pay_info']['total']);
                $refund_total = isset($invoice_data['pay_info']['refund_total']) ? floatval($invoice_data['pay_info']['refund_total']) : 0;
                $is_refund = isset($invoice_data['pay_info']['is_refund']) ? $invoice_data['pay_info']['is_refund'] : 0;

                if ($total > 0 && ($refund_total <= 0 || $refund_total < $total * 0.999)) {
                    // 计算可开票金额 = 订单总金额 - 退款金额
                    $invoice_total = $total - $refund_total;

                    if ($invoice_total > 0) {
                        // 更新数据库
                        $model_pay = M('SubPay');
                        $updateData = [
                            'invoice_total' => number_format($invoice_total, 2, '.', '')
                        ];

                        if ($is_refund == 1) {
                            $updateData['is_refund'] = 1;
                            $updateData['refund_total'] = number_format($refund_total, 2, '.', '');
                        }

                        $model_pay->where(['orderid' => $order_id])->save($updateData);

                        \Think\Log::record('修复游客订单可开票金额: ' . $order_id . ', 订单总额=' . $total .
                            ', 退款金额=' . $refund_total . ', 可开票金额=' . $invoice_total, 'INFO');

                        // 更新内存中的数据
                        $invoice_data['pay_info']['invoice_total'] = number_format($invoice_total, 2, '.', '');
                        $invoice_data['remaining_amount'] = number_format($invoice_total - floatval($invoice_data['total_invoiced']), 2, '.', '');
                    } else {
                        \Think\Log::record('游客订单可开票金额计算为0: ' . $order_id, 'INFO');
                        $this->error('This order has no available amount for invoice');
                    }
                } else {
                    \Think\Log::record('游客订单可开票金额为0: ' . $order_id, 'INFO');
                    $this->error('This order has no available amount for invoice');
                }
            }

            // 4. 检查可开票金额
            if (floatval($invoice_data['remaining_amount']) <= 0) {
                \Think\Log::record('游客订单剩余可开票金额为0: ' . $order_id, 'INFO');
                $this->error('The order amount has reached the maximum invoice amount or has been fully refunded');
            }

            // 处理金额格式化
            $invoice_data['pay_info']['total'] = number_format($invoice_data['pay_info']['total'], 2, '.', '');
            $invoice_data['total_invoiced'] = number_format($invoice_data['total_invoiced'], 2, '.', '');
            $invoice_data['remaining_amount'] = number_format($invoice_data['remaining_amount'], 2, '.', '');

            // 处理支付状态和支付方式
            $paymentStatus = [];
            if (isset($invoice_data['pay_info']['status'])) {
                $status = intval($invoice_data['pay_info']['status']);
                $paymentStatus['code'] = $status;
                $paymentStatus['text'] = OnlinePaymentStatusConstants::getStatusText($status);
                $paymentStatus['class'] = OnlinePaymentStatusConstants::getStatusClass($status);
                $paymentStatus['is_success'] = OnlinePaymentStatusConstants::isSuccess($status);
            }

            // 处理支付方式
            $paymentMethod = [];
            if (isset($invoice_data['pay_info']['paytype'])) {
                $paytype = $invoice_data['pay_info']['paytype'];
                $paymentMethod['code'] = $paytype;

                // 根据不同的支付方式设置显示文本
                switch ($paytype) {
                    case 'bank':
                        $paymentMethod['text'] = '银行转账';
                        break;
                    case 'alipay':
                        $paymentMethod['text'] = '支付宝';
                        break;
                    case 'wechat':
                        $paymentMethod['text'] = '微信支付';
                        break;
                    case 'paypal':
                        $paymentMethod['text'] = 'PayPal';
                        break;
                    default:
                        $paymentMethod['text'] = $paytype;
                }
            }

            // 获取发票项目选项
            $invoiceItemOptions = $this->getInvoiceItemService()->getInvoiceItemOptions();

            // 将所有数据传递给视图
            $this->assign('pay_info', $invoice_data['pay_info']);
            $this->assign('invoice_history', $invoice_data['invoice_history']);
            $this->assign('total_invoiced', $invoice_data['total_invoiced']);
            $this->assign('remaining_amount', $invoice_data['remaining_amount']);
            $this->assign('is_guest', true); // 标记为游客模式
            $this->assign('user_info', []); // 游客模式下的空用户信息
            $this->assign('payment_status', $paymentStatus); // 增加支付状态信息
            $this->assign('payment_method', $paymentMethod); // 增加支付方式信息
            $this->assign('invoice_item_options', $invoiceItemOptions); // 增加发票项目选项

            $this->display('onlineInvoice'); // 复用同一个视图模板
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 保存游客发票申请
     */
    public function saveGuest()
    {
        try {
            $post = I('post.');

            // 增强输入过滤
            $post['invoice_title'] = htmlspecialchars(trim($post['invoice_title']));
            $post['buyer_tax_num'] = preg_replace('/[^0-9A-Z]/', '', $post['buyer_tax_num']);
            $post['goods_info'] = htmlspecialchars(trim($post['goods_info']));
            $post['buyer_email'] = filter_var(trim($post['buyer_email']), FILTER_SANITIZE_EMAIL);
            $post['is_guest'] = true; // 标记为游客申请

            // 处理新增字段
            if (isset($post['buyer_tel'])) {
                $post['buyer_tel'] = htmlspecialchars(trim($post['buyer_tel']));
            }
            if (isset($post['remark'])) {
                $post['remark'] = htmlspecialchars(trim($post['remark']));
            }

            // 使用模型处理发票申请
            $model = D('MemberOaInvoice');
            $result = $model->addNewGuestPay($post);

            // 处理返回结果
            if ($result['status'] == 1) {
                // 成功提交
                $this->success($result['info'], U('Home/Bill/successful', ['id' => $result['id']]));
            } else {
                // 提交失败
                $this->error($result['info']);
            }
        } catch (\Exception $e) {
            // 异常处理
            $this->error('Invoice application failed: ' . $e->getMessage());
        }
    }

    public function saveOffline()
    {
        try {
            // 获取用户ID
            $userId = str_auth(cookie('userid'), 'DECODE');
            if (empty($userId)) {
                $this->error('Please login first');
            }

            // 获取表单数据
            $post = I('post.');

            // 检查注册记录ID是否存在
            if (empty($post['reg_id'])) {
                $this->error('Registration ID cannot be empty');
            }

            // 增强输入过滤
            $post['invoice_title'] = htmlspecialchars(trim($post['invoice_title']));
            $post['buyer_tax_num'] = preg_replace('/[^0-9A-Z]/', '', $post['buyer_tax_num']);
            $post['goods_info'] = htmlspecialchars(trim($post['goods_info']));
            $post['buyer_email'] = filter_var(trim($post['buyer_email']), FILTER_SANITIZE_EMAIL);
            $post['user_id'] = $userId; // 添加用户ID

            // 处理新增字段
            if (isset($post['buyer_tel'])) {
                $post['buyer_tel'] = htmlspecialchars(trim($post['buyer_tel']));
            }
            if (isset($post['remark'])) {
                $post['remark'] = htmlspecialchars(trim($post['remark']));
            }

            // 验证金额
            if (!isset($post['amount']) || !is_numeric($post['amount']) || floatval($post['amount']) <= 0) {
                $this->error('Invoice amount must be a positive number');
            }

            // 验证发票类型
            if (empty($post['invoice_type']) || !InvoiceTypeConstants::isValidType($post['invoice_type'])) {
                $this->error('Please select a valid invoice type');
            }

            // 使用模型处理发票申请
            $model = D('MemberOaInvoice');
            $result = $model->addNewOfflinePay($post);

            // 处理返回结果
            if ($result['status'] == 1) {
                // 注意：我们不再需要在这里调用状态同步服务，因为在 MemberOaInvoiceModel::addNewOfflinePay 方法中
                // 已经通过 AuditInvoiceRelationService 自动关联了查账记录并更新了状态

                // 记录日志
                \Think\Log::record('线下发票申请成功，ID: ' . $result['id'], 'INFO');

                // 成功提交
                $this->redirect('successful', ['id' => $result['id']]);
            } else {
                // 提交失败
                $this->error($result['info']);
            }
        } catch (\Exception $e) {
            // 异常处理
            $this->error('Invoice application failed: ' . $e->getMessage());
        }
    }

    /**
     * AJAX方式保存线下发票申请信息
     * 返回JSON格式的响应，而不是重定向
     */
    public function saveOfflineAjax()
    {
        // 禁止直接访问该方法
        if (!IS_AJAX) {
            \Think\Log::record('非AJAX请求尝试访问 saveOfflineAjax 方法', 'WARN');
            $this->error('非法请求');
            return;
        }

        // 检查用户权限
        $userId = str_auth(cookie('userid'), 'DECODE');
        if (empty($userId)) {
            \Think\Log::record('用户未登录尝试提交线下发票申请', 'WARN');
            $this->ajaxReturn(['status' => 0, 'message' => '您的登录已过期，请重新登录']);
            return;
        }

        try {
            $post = I('post.');

            // 检查注册记录ID是否存在
            if (empty($post['reg_id'])) {
                \Think\Log::record('线下发票申请缺少注册记录ID，用户ID: ' . $userId, 'WARN');
                $this->ajaxReturn(['status' => 0, 'message' => '注册记录ID不能为空']);
                return;
            }

            // 验证注册记录是否存在
            $regModel = D('confRegister');
            $regInfo = $regModel->where(['id' => $post['reg_id']])->find();
            if (empty($regInfo)) {
                \Think\Log::record('线下发票申请注册记录不存在，ID: ' . $post['reg_id'] . ', 用户ID: ' . $userId, 'WARN');
                $this->ajaxReturn(['status' => 0, 'message' => '注册记录不存在']);
                return;
            }

            // 验证注册记录是否属于当前用户
            if ($regInfo['userid'] != $userId) {
                \Think\Log::record('用户尝试提交不属于自己的线下发票申请，用户ID: ' . $userId . ', 注册记录ID: ' . $post['reg_id'] . ', 注册记录所属用户ID: ' . $regInfo['userid'], 'WARN');
                $this->ajaxReturn(['status' => 0, 'message' => '您无权申请该转账的发票']);
                return;
            }

            // 增强输入过滤
            $post['invoice_title'] = htmlspecialchars(trim($post['invoice_title']));
            $post['buyer_tax_num'] = preg_replace('/[^0-9A-Z]/', '', $post['buyer_tax_num']);
            $post['goods_info'] = htmlspecialchars(trim($post['goods_info']));
            $post['buyer_email'] = filter_var(trim($post['buyer_email']), FILTER_SANITIZE_EMAIL);
            $post['user_id'] = $userId; // 添加用户ID

            // 处理新增字段
            if (isset($post['buyer_tel'])) {
                $post['buyer_tel'] = htmlspecialchars(trim($post['buyer_tel']));
            }
            if (isset($post['remark'])) {
                $post['remark'] = htmlspecialchars(trim($post['remark']));
            }

            // 验证金额
            if (!isset($post['amount']) || !is_numeric($post['amount']) || floatval($post['amount']) <= 0) {
                $this->ajaxReturn(['status' => 0, 'message' => '发票金额必须是正数']);
                return;
            }

            // 验证发票类型
            if (empty($post['invoice_type']) || !\Common\Lib\InvoiceTypeConstants::isValidType($post['invoice_type'])) {
                $this->ajaxReturn(['status' => 0, 'message' => '请选择有效的发票类型']);
                return;
            }

            // 使用模型处理发票申请
            $model = D('MemberOaInvoice');
            $result = $model->addNewOfflinePay($post);

            // 处理返回结果
            if ($result['status'] == 1) {
                // 注意：我们不再需要在这里调用状态同步服务，因为在 MemberOaInvoiceModel::addNewOfflinePay 方法中
                // 已经通过 AuditInvoiceRelationService 自动关联了查账记录并更新了状态

                // 记录日志
                \Think\Log::record('线下发票申请成功（AJAX），ID: ' . $result['id'], 'INFO');

                // 获取更新后的转账信息
                try {
                    // 尝试获取转账信息
                    $invoice_data = $this->invoiceService->isOfflineOrderCanInvoice(['reg_id' => $post['reg_id'], 'user_id' => $userId]);
                } catch (\Exception $e) {
                    // 记录异常信息
                    \Think\Log::record('获取转账信息异常: ' . $e->getMessage(), 'WARN');

                    // 如果是因为剩余金额为0导致的异常，则手动构造数据
                    if (strpos($e->getMessage(), '该转账已无剩余可开票金额') !== false) {
                        // 获取转账信息
                        $transferModel = D('SubTransfer');
                        $transferInfo = $transferModel->where(['reg_id' => $post['reg_id']])->find();

                        // 检查转账信息是否存在
                        if (empty($transferInfo)) {
                            $this->ajaxReturn(['status' => 0, 'message' => '转账信息不存在']);
                            return;
                        }

                        // 检查转账信息是否属于当前用户
                        $regModel = D('confRegister');
                        $regInfo = $regModel->where(['id' => $post['reg_id']])->find();

                        if (empty($regInfo)) {
                            $this->ajaxReturn(['status' => 0, 'message' => '注册信息不存在']);
                            return;
                        }

                        if ($regInfo['userid'] != $userId) {
                            $this->ajaxReturn(['status' => 0, 'message' => '您无权申请该转账的发票']);
                            return;
                        }

                        // 获取发票历史记录
                        $invoice_model = M('OaInvoice');
                        $invoice_history = $invoice_model->where(['reg_id' => $post['reg_id']])->select();

                        // 计算已开票金额
                        $total_invoiced = 0;
                        foreach ($invoice_history as $invoice) {
                            $total_invoiced += floatval($invoice['amount']);
                        }

                        // 构造数据
                        $invoice_data = [
                            'reg_info' => $regInfo,
                            'transfer_info' => $transferInfo,
                            'invoice_history' => $invoice_history,
                            'total_invoiced' => $total_invoiced,
                            'remaining_amount' => 0 // 剩余金额为0
                        ];

                        // 记录构造的数据
                        \Think\Log::record('手动构造的发票数据: ' . json_encode($invoice_data, JSON_UNESCAPED_UNICODE), 'INFO');
                    } else {
                        // 如果是其他异常，则抛出
                        throw $e;
                    }
                }

                // 格式化金额
                $invoice_data['remaining_amount'] = number_format(floatval($invoice_data['remaining_amount']), 2, '.', '');
                $invoice_data['total_invoiced'] = number_format(floatval($invoice_data['total_invoiced']), 2, '.', '');
                if (isset($invoice_data['transfer_info']['total'])) {
                    $invoice_data['transfer_info']['total'] = number_format(floatval($invoice_data['transfer_info']['total']), 2, '.', '');
                }

                // 返回成功响应
                $response = [
                    'status' => 1,
                    'message' => '发票申请提交成功',
                    'invoice_id' => $result['id'],
                    'invoice_data' => [
                        'reg_info' => $invoice_data['reg_info'],
                        'transfer_info' => $invoice_data['transfer_info'],
                        'invoice_history' => $invoice_data['invoice_history'],
                        'total_invoiced' => $invoice_data['total_invoiced'],
                        'remaining_amount' => $invoice_data['remaining_amount']
                    ]
                ];

                $this->ajaxReturn($response);
            } else {
                // 返回失败响应
                $this->ajaxReturn(['status' => 0, 'message' => $result['info']]);
            }
        } catch (\Exception $e) {
            // 异常处理
            $this->ajaxReturn(['status' => 0, 'message' => '发票申请失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 发票申请成功页面
     * @param int $id 发票ID
     */
    public function successful()
    {
        $id = I('get.id', 0, 'intval');
        if (empty($id)) {
            $this->error('参数错误');
        }

        // 获取发票信息
        $model = D('MemberOaInvoice');
        $invoice = $model->find($id);
        if (empty($invoice)) {
            $this->error('发票信息不存在');
        }

        // 传递数据到视图
        $this->assign('invoice_id', $id);
        $this->assign('order_id', $invoice['order_id']);
        $this->display();
    }
    /**
     * 发票详情页面
     * @param int $id 发票ID
     */
    public function invoiceDetail()
    {
        $id = I('get.id', 0, 'intval');
        if (empty($id)) {
            $this->error('参数错误');
        }

        // 获取用户ID
        $userId = str_auth(cookie('userid'), 'DECODE');

        // 获取发票信息
        $model = D('MemberOaInvoice');
        $invoice = $model->find($id);

        // 检查发票是否存在
        if (empty($invoice)) {
            $this->error('发票信息不存在');
        }

        // 检查发票是否属于当前用户
        if ($invoice['user_id'] != $userId) {
            $this->error('您无权查看此发票信息');
        }

        // 获取相关信息
        if ($invoice['pay_type'] == 'transfer') {
            // 线下转账发票
            $transferModel = D('SubTransfer');
            $transferInfo = $transferModel->find($invoice['transfer_id']);
            $this->assign('transfer_info', $transferInfo);
        } else {
            // 在线支付发票
            $payModel = D('MemberPay');
            $payInfo = $payModel->find($invoice['pay_id']);
            $this->assign('pay_info', $payInfo);
        }

        // 添加状态文本和样式类
        $invoice['status_text'] = Status::getStatusText($invoice['status']);
        $invoice['status_class'] = Status::getStatusClass($invoice['status']);

        // 传递数据到视图
        $this->assign('invoice', $invoice);
        $this->display();
    }

    /**
     * 编辑发票页面
     * @param int $id 发票ID
     */
    public function editInvoice()
    {
        $id = I('get.id', 0, 'intval');
        if (empty($id)) {
            $this->error('参数错误');
        }

        // 获取用户ID
        $userId = str_auth(cookie('userid'), 'DECODE');

        // 获取发票信息
        $model = D('MemberOaInvoice');
        $invoice = $model->find($id);

        // 检查发票是否存在
        if (empty($invoice)) {
            $this->error('发票信息不存在');
        }

        // 检查发票是否属于当前用户
        if ($invoice['user_id'] != $userId) {
            $this->error('您只能编辑自己的发票申请');
        }

        // 检查发票状态是否可编辑
        if (!Status::isEditable($invoice['status'])) {
            $this->error('只有初审未通过的发票申请可以编辑');
        }

        // 获取相关信息
        if ($invoice['pay_type'] == 'transfer') {
            // 线下转账发票
            $transferModel = D('SubTransfer');
            $transferInfo = $transferModel->find($invoice['transfer_id']);
            $this->assign('transfer_info', $transferInfo);
        } else {
            // 在线支付发票
            $payModel = D('MemberPay');
            $payInfo = $payModel->find($invoice['pay_id']);
            $this->assign('pay_info', $payInfo);
        }

        // 添加状态文本和样式类
        $invoice['status_text'] = Status::getStatusText($invoice['status']);
        $invoice['status_class'] = Status::getStatusClass($invoice['status']);

        // 传递数据到视图
        $this->assign('invoice', $invoice);
        $this->display();
    }

    /**
     * 更新发票信息
     * 只允许修改指定字段：invoice_title、buyer_tax_num、buyer_phone、buyer_email、
     * buyer_account、buyer_account_name、goods_info、invoice_type、remark
     */
    public function updateInvoice()
    {
        try {
            // 记录调试信息 - 开始处理
            \Think\Log::record('[控制器] 开始处理发票更新请求', 'DEBUG');

            $post = I('post.');
            \Think\Log::record('[控制器] 接收到的表单数据: ' . json_encode($post, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 检查必要参数
            if (empty($post['id'])) {
                \Think\Log::record('[控制器] 缺少必要参数: id', 'ERR');
                $this->error('缺少必要参数: id');
                return;
            }

            // 获取用户ID
            $userId = str_auth(cookie('userid'), 'DECODE');
            \Think\Log::record('[控制器] 当前用户ID: ' . $userId, 'DEBUG');

            if (empty($userId)) {
                \Think\Log::record('[控制器] 用户未登录或会话过期', 'ERR');
                $this->error('您的登录已过期，请重新登录');
                return;
            }

            // 调用服务类处理发票更新
            \Think\Log::record('[控制器] 调用服务类处理发票更新', 'DEBUG');
            $invoiceService = new \Home\Service\InvoiceService();
            $result = $invoiceService->updateInvoice($post['id'], $userId, $post);

            // 记录服务类返回结果
            \Think\Log::record('[控制器] 服务类返回结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 处理结果
            if ($result['status'] === 1) {
                \Think\Log::record('[控制器] 发票更新成功', 'DEBUG');
                $this->success($result['message'], U('Home/Bill/BillList'));
            } else {
                \Think\Log::record('[控制器] 发票更新失败: ' . $result['message'], 'ERR');
                $this->error($result['message']);
            }
        } catch (\Exception $e) {
            $errorMsg = '发票信息更新失败: ' . $e->getMessage();
            $errorTrace = '错误跟踪: ' . $e->getTraceAsString();

            // 记录错误日志
            \Think\Log::record('[控制器] ' . $errorMsg, 'ERR');
            \Think\Log::record('[控制器] ' . $errorTrace, 'ERR');

            $this->error($errorMsg);
        }
    }

    /**
     * 删除发票申请
     */
    public function deleteInvoice()
    {
        try {
            $id = I('get.id', 0, 'intval');
            if (empty($id)) {
                $this->error('参数错误');
            }

            // 获取用户ID
            $userId = str_auth(cookie('userid'), 'DECODE');

            // 获取发票信息
            $model = D('MemberOaInvoice');
            $invoice = $model->find($id);

            // 检查发票是否存在
            if (empty($invoice)) {
                $this->error('发票信息不存在');
            }

            // 检查发票是否属于当前用户
            if ($invoice['user_id'] != $userId) {
                $this->error('您只能删除自己的发票申请');
            }

            // 检查发票状态是否可删除
            if (!Status::isDeletable($invoice['status'])) {
                $this->error('只有初审未通过的发票申请可以删除');
            }

            // 删除发票申请
            $result = $model->where(['id' => $id])->delete();

            if ($result) {
                $this->success('发票申请删除成功', U('Home/Bill/BillList'));
            } else {
                $this->error('发票申请删除失败');
            }
        } catch (\Exception $e) {
            $this->error('发票申请删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 下载发票
     * 使用公共的发票下载服务类处理下载逻辑
     */
    public function downloadInvoice()
    {
        try {
            $id = I('get.id', 0, 'intval');
            $attachmentId = I('get.attachment_id', null, 'intval');
            $attachmentType = I('get.type', null, 'intval');

            if (empty($id)) {
                $this->error('参数错误');
            }

            // 获取用户ID
            $userId = str_auth(cookie('userid'), 'DECODE');

            // 使用发票下载服务类
            $downloadService = new \Common\Service\InvoiceDownloadService();
            $result = $downloadService->downloadInvoice($id, $userId, $attachmentType, $attachmentId);

            if ($result['status'] == 0) {
                $this->error($result['message']);
            }

            // 输出文件内容
            $downloadService->outputFile($result['data']);
        } catch (\Exception $e) {
            $this->error('发票下载失败: ' . $e->getMessage());
        }
    }

    /**
     * 显示发票附件选择页面
     * 当一个发票有多个附件时，显示选择界面
     */
    public function invoiceAttachments()
    {
        try {
            $id = I('get.id', 0, 'intval');
            if (empty($id)) {
                $this->error('参数错误');
            }

            // 获取用户ID
            $userId = str_auth(cookie('userid'), 'DECODE');

            // 使用发票下载服务类获取附件列表
            $downloadService = new \Common\Service\InvoiceDownloadService();
            $result = $downloadService->getInvoiceAttachments($id, $userId);

            if ($result['status'] == 0) {
                $this->error($result['message']);
            }

            // 始终显示附件选择界面，即使只有一个附件
            // 这样用户可以看到附件类型信息

            // 传递数据到视图
            $this->assign('invoice', $result['data']['invoice']);
            $this->assign('attachments', $result['data']['attachments']);
            $this->display();
        } catch (\Exception $e) {
            $this->error('获取发票附件失败: ' . $e->getMessage());
        }
    }

    //订单列表页（包括在线支付订单和线下汇款订单）
    public function PayList()
    {
        // 获取选项卡参数
        $tab = I('get.tab', 'online');
        // 获取用户ID
        $user_id = str_auth(cookie('userid'), 'DECODE');

        // 获取搜索条件
        $search_order_id = I('get.order_id', '', 'trim'); // 按订单号搜索
        $search_event_name = I('get.event_name', '', 'trim'); // 按会议名称搜索
        $search_paper_id = I('get.paper_id', '', 'trim'); // 按文章ID搜索

        // 调用模型方法获取分页数据
        $model = D('MemberPay');
        $result = $model->getPayListWithPagination($user_id, $search_order_id, $search_event_name, $search_paper_id);

        // 处理在线支付订单的发票申请资格
        if (!empty($result['pay_list'])) {
            foreach ($result['pay_list'] as &$order) {
                // 检查是否可以申请发票
                $eligibilityResult = InvoiceHelper::canApplyInvoiceForOnlinePayment($order);
                $order['can_apply_invoice'] = $eligibilityResult['eligible'];
                $order['invoice_reason'] = $eligibilityResult['reason'];
                $order['remaining_amount'] = $eligibilityResult['remaining_amount'];

                // 生成发票申请按钮 HTML - 使用核验按钮（不显示金额）
                $applyUrl = U('Home/Bill/onlineInvoice', ['order_id' => $order['orderid']]);
                $order['invoice_button'] = InvoiceHelper::getInvoiceButtonHtml($eligibilityResult, $applyUrl, [], true);
            }
            unset($order); // 释放引用
        }

        // 获取线下汇款数据
        $transfer_model = D('SubTransfer');

        // 使用新方法获取用户转账记录
        $transfer_result = $transfer_model->getUserTransferListWithPagination($user_id, $search_event_name, $search_paper_id);

        // 处理线下转账的发票申请资格
        if (!empty($transfer_result['list'])) {
            foreach ($transfer_result['list'] as &$transfer) {
                // 检查是否可以申请发票
                $eligibilityResult = InvoiceHelper::canApplyInvoiceForOfflineTransfer($transfer);
                $transfer['can_apply_invoice'] = $eligibilityResult['eligible'];
                $transfer['invoice_reason'] = $eligibilityResult['reason'];
                $transfer['remaining_amount'] = $eligibilityResult['remaining_amount'];

                // 生成发票申请按钮 HTML - 使用核验按钮（不显示金额）
                $applyUrl = U('Home/Bill/offlineInvoice', ['reg_id' => $transfer['reg_id']]);
                $transfer['invoice_button'] = InvoiceHelper::getInvoiceButtonHtml($eligibilityResult, $applyUrl, [], true);
            }
            unset($transfer); // 释放引用
        }

        $this->assign('pay_list', $result['pay_list']); // 订单列表
        $this->assign('page', $result['page_html']); // 分页HTML
        $this->assign('search_order_id', $result['search_order_id']); // 搜索条件
        $this->assign('search_event_name', $result['search_event_name']); // 搜索条件
        $this->assign('search_paper_id', $result['search_paper_id']); // 搜索条件

        // 分配线下汇款相关变量到视图
        $this->assign('transfer_list', $transfer_result['list']); // 线下汇款列表
        $this->assign('transfer_page', $transfer_result['page']); // 线下汇款分页HTML
        $this->assign('search_event_name', $search_event_name); // 会议名称搜索条件
        $this->assign('search_paper_id', $search_paper_id); // 文章ID搜索条件

        // 分配选项卡参数
        $this->assign('active_tab', $tab);

        // 引入发票按钮样式文件
        $this->assign('invoice_css', true);

        // 渲染视图
        $this->display();
    }
    //tp3.2 发票列表页
    public function BillList()
    {
        // 显示该用户所有申请的发票信息
        $user_id = str_auth(cookie('userid'), 'DECODE');
        $model = D('MemberOaInvoice');

        // 获取搜索条件
        $search_title = I('get.invoice_title', '', 'trim'); // 获取发票抬头搜索条件

        // 调用模型方法获取分页数据
        $result = $model->getInvoiceListWithPagination($user_id, $search_title);

        // 处理发票状态信息
        if (!empty($result['invoice_list'])) {
            foreach ($result['invoice_list'] as &$invoice) {
                // 处理状态为0的特殊情况（历史数据兼容）
                if ($invoice['status'] === 0 || $invoice['status'] === '0') {
                    $invoice['status'] = Status::INVOICE_PENDING; // 转换为新的状态码
                }

                // 添加状态文本和样式类
                $invoice['status_text'] = Status::getStatusText($invoice['status']);
                $invoice['status_class'] = Status::getStatusClass($invoice['status']);
            }
            unset($invoice); // 释放引用
        }

        // 分配变量到视图
        $this->assign('invoice_list', $result['invoice_list']); // 发票列表
        $this->assign('page', $result['page_html']); // 分页HTML
        $this->assign('search_title', $result['search_title']); // 搜索条件

        // 渲染视图
        $this->display();
    }

    /**
     * 线下转账发票申请入口
     * 使用 SubTransferModel 获取转账记录信息
     */
    public function offlineInvoice()
    {
        try {
            // 获取用户ID
            $userId = str_auth(cookie('userid'), 'DECODE');
            if (empty($userId)) {
                // 记录日志
                \Think\Log::record('用户未登录尝试访问线下发票申请页面', 'WARN');
                $this->error('请先登录再访问此页面');
                return;
            }

            // 获取注册记录ID
            $regId = I('get.reg_id', 0, 'intval');
            if (empty($regId)) {
                \Think\Log::record('线下发票申请缺少注册记录ID，用户ID: ' . $userId, 'WARN');
                $this->error('注册记录ID不能为空');
                return;
            }

            // 验证注册记录是否存在
            $regModel = D('confRegister');
            $regInfo = $regModel->where(['id' => $regId])->find();
            if (empty($regInfo)) {
                \Think\Log::record('线下发票申请注册记录不存在，ID: ' . $regId . ', 用户ID: ' . $userId, 'WARN');
                $this->error('注册记录不存在');
                return;
            }

            // 验证注册记录是否属于当前用户
            if ($regInfo['userid'] != $userId) {
                \Think\Log::record('用户尝试访问不属于自己的线下发票申请，用户ID: ' . $userId . ', 注册记录ID: ' . $regId . ', 注册记录所属用户ID: ' . $regInfo['userid'], 'WARN');
                $this->error('您无权访问该注册记录的发票申请');
                return;
            }

            // 准备参数
            $post = [
                'reg_id' => $regId,
                'user_id' => $userId
            ];

            // 调用服务类检查是否可以开票
            $invoice_data = $this->invoiceService->isOfflineOrderCanInvoice($post);

            // 检查是否还有可开票金额
            if ($invoice_data['remaining_amount'] == 0) {
                \Think\Log::record('线下发票申请剩余金额为0，用户ID: ' . $userId . ', 注册记录ID: ' . $regId, 'INFO');
                $this->error('该转账已无剩余可开票金额');
                return;
            }

            // 格式化金额显示 - 使用 sprintf 而不是 number_format 以避免整数部分前面添加0
            $invoice_data['remaining_amount'] = sprintf('%.2f', floatval($invoice_data['remaining_amount']));
            $invoice_data['total_invoiced'] = sprintf('%.2f', floatval($invoice_data['total_invoiced']));

            // 使用服务类返回的转账信息
            $transferInfo = $invoice_data['transfer_info'];

            // 格式化转账金额
            $transferInfo['total'] = sprintf('%.2f', floatval($transferInfo['total']));

            // 处理货币类型，将数字转换为货币符号和文本
            if (isset($transferInfo['currency'])) {
                // 使用CurrencyTypeConstants类获取货币符号和文本
                $transferInfo['currency_symbol'] = \Common\Lib\CurrencyTypeConstants::getCurrencySymbol($transferInfo['currency']);
                $transferInfo['currency_text'] = \Common\Lib\CurrencyTypeConstants::getCurrencyText($transferInfo['currency']);
            }

            // 处理转账状态
            $transferStatus = [];
            if (isset($transferInfo['status'])) {
                $status = intval($transferInfo['status']);
                $transferStatus['code'] = $status;
                $transferStatus['text'] = TransferStatus::getStatusText($status);
                $transferStatus['class'] = TransferStatus::getStatusClass($status);
            }

            // 处理发票历史记录的状态
            if (!empty($invoice_data['invoice_history'])) {
                foreach ($invoice_data['invoice_history'] as &$invoice) {
                    // 处理状态为0的特殊情况（历史数据兼容）
                    if ($invoice['status'] === 0 || $invoice['status'] === '0') {
                        $invoice['status'] = Status::INVOICE_PENDING; // 转换为新的状态码
                    }

                    // 添加状态文本和样式类
                    $invoice['status_text'] = Status::getStatusText($invoice['status']);
                    $invoice['status_class'] = Status::getStatusClass($invoice['status']);
                }
                unset($invoice); // 释放引用
            }

            // 获取发票策略信息
            $maxInvoiceTimes = \Common\Lib\InvoiceEligibilityConfig::getMaxInvoiceTimes();
            $allowPartialInvoice = \Common\Lib\InvoiceEligibilityConfig::isPartialInvoiceAllowed();

            // 准备发票策略提示信息
            $invoiceStrategyInfo = [];

            // 判断是否可以开多张发票
            if ($maxInvoiceTimes === null) {
                $invoiceStrategyInfo['multiple_invoices'] = true;
                $invoiceStrategyInfo['max_times'] = '\u4e0d\u9650\u5236';
            } else {
                $invoiceStrategyInfo['multiple_invoices'] = $maxInvoiceTimes > 1;
                $invoiceStrategyInfo['max_times'] = $maxInvoiceTimes;
            }

            // 判断是否可以部分金额开票
            $invoiceStrategyInfo['partial_invoice'] = $allowPartialInvoice;

            // 判断已申请次数
            $invoiceStrategyInfo['applied_times'] = count($invoice_data['invoice_history']);

            // 判断是否还可以继续申请 - 只考虑最大申请次数，其他条件由前端处理
            $invoiceStrategyInfo['can_apply_more'] = $maxInvoiceTimes === null || $invoiceStrategyInfo['applied_times'] < $maxInvoiceTimes;

            // 获取发票项目选项
            $invoiceItemOptions = $this->getInvoiceItemService()->getInvoiceItemOptions();

            // 调试信息
            \Think\Log::record('线下发票项目选项: ' . json_encode($invoiceItemOptions, JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 分配数据到模板
            $this->assign('reg_info', $invoice_data['reg_info']);
            $this->assign('reg_id', $regId); // 直接传递 reg_id 到视图
            $this->assign('transfer_info', $transferInfo);
            $this->assign('transfer_status', $transferStatus);
            $this->assign('invoice_history', $invoice_data['invoice_history']);
            $this->assign('total_invoiced', $invoice_data['total_invoiced']);
            $this->assign('remaining_amount', $invoice_data['remaining_amount']);
            $this->assign('invoice_strategy', $invoiceStrategyInfo);
            $this->assign('invoice_item_options', $invoiceItemOptions); // 增加发票项目选项

            // 记录成功访问日志
            \Think\Log::record('用户成功访问线下发票申请页面，用户ID: ' . $userId . ', 注册记录ID: ' . $regId . ', 剩余金额: ' . $invoice_data['remaining_amount'], 'INFO');

            $this->display();
        } catch (\Exception $e) {
            // 记录异常日志
            \Think\Log::record('线下发票申请异常: ' . $e->getMessage() . ', 调用栈: ' . $e->getTraceAsString(), 'ERR');
            $this->error($e->getMessage());
        }
    }
    /**
     * 显示所有成功支付的订单信息（包括线上和线下），只面向中文用户，界面用中文
     * 具体实现： 1 用选卡分别呈现，在线支付成功的订单， 和转账汇款已经审核成功的订单
     * 2：在线支付成功订单的标准为：sub_pay表中 status==20 , userid= 当前Userid 的记录列表
     * 3：线下汇款已经审核成功的订单的标准为：sub_transfer 表中 status==10 信息。
     */
    public function sucessBillList() {}
}
