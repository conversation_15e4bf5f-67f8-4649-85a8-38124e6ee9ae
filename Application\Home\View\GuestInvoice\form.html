<extend name="public:guest_mini" />
<block name="title">
    <title>申请新发票</title>
</block>

<block name="style">
    <link href="__MODULE__/GuestInvoice/invoice-form.css" rel="stylesheet" type="text/css">
</block>
<block name="main">
    <div class="container">

        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="font-weight-bold">
                    <i class="fas fa-file-invoice-dollar mr-2"></i> 发票申请
                </h2>
                <p class="text-muted">请填写以下信息申请发票</p>
            </div>
        </div>
        <div class="holder my-4">
            <ul class="SteppedProgress">
                <li class="complete"><span>验证订单</span></li>
                <li class="complete"><span>填写开票信息</span></li>
                <li><span>等待开票</span></li>
                <li><span>查看结果</span></li>
            </ul>
        </div>


        <!-- 导航链接 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center bg-light py-2">
                        <li class="breadcrumb-item"><a href="{:U('Home/GuestInvoice/index')}">首页</a></li>
                        <li class="breadcrumb-item active">发票申请</li>
                    </ol>
                </nav>
            </div>
        </div>


        <div class="row">
            <!-- 左侧账单信息 -->
            <div class="col-lg-4">
                <div class="card invoice-info-card mb-4">
                    <div class="card-body">
                        <h4 class="card-title mb-4">账单信息</h4>

                        <div class="border-bottom pb-3 mb-3">
                            <div class="row">
                                <div class="col-sm-12">
                                    <p class="text-muted mb-2">订单号</p>
                                    <h5 class="font-size-15 mb-0">{$pay_info.orderid}</h5>
                                </div>

                            </div>
                        </div>


                        <div class="border-bottom pb-3 mb-3">
                            <p class="text-muted mb-2">会议名称</p>
                            <h5 class="font-size-15 mb-0">
                                <notempty name="pay_info.event_acronym">{$pay_info.event_acronym}
                                    <else />未知会议
                                </notempty>
                            </h5>
                        </div>

                        <div class="border-bottom pb-3 mb-3">
                            <p class="text-muted mb-2">文章编号</p>
                            <h5 class="font-size-15 mb-0">
                                <notempty name="pay_info.paper_id">{$pay_info.paper_id}
                                    <else />无文章编号
                                </notempty>
                            </h5>
                        </div>

                        <div class="border-bottom pb-3 mb-3">
                            <p class="text-muted mb-2">联系邮箱</p>
                            <h5 class="font-size-15 mb-0">
                                <notempty name="pay_info.email">{$pay_info.email}
                                    <else />无邮箱信息
                                </notempty>
                            </h5>
                        </div>

                        <div class="pt-2">
                            <!-- 金额信息卡片 - 使用更清晰的布局 -->
                            <div class="card bg-light border-0 mb-3">
                                <div class="card-body p-3">
                                    <h6 class="card-title text-primary mb-3">
                                        <i class="fas fa-money-bill-wave mr-2"></i>金额明细
                                    </h6>

                                    <div class="row mb-2">
                                        <div class="col-6 text-muted">订单总金额:</div>
                                        <div class="col-6 text-right font-weight-bold">¥{$pay_info.total}</div>
                                    </div>

                                    <!-- 退款信息显示 - 只在有退款时显示 -->
                                    <if condition="null !== $pay_info['is_refund'] && $pay_info['is_refund'] eq 1">
                                        <div class="row mb-2">
                                            <div class="col-6 text-muted">退款金额:</div>
                                            <div class="col-6 text-right">
                                                <span class="text-danger font-weight-bold">- ¥{$pay_info.refund_total}</span>
                                            </div>
                                        </div>
                                    </if>

                                    <!-- 已开票金额 - 只在有已开票金额时显示 -->
                                    <if condition="$total_invoiced gt 0">
                                        <div class="row mb-2">
                                            <div class="col-6 text-muted">已开票金额:</div>
                                            <div class="col-6 text-right">
                                                <span class="text-primary">- ¥{$total_invoiced}</span>
                                            </div>
                                        </div>
                                    </if>

                                    <!-- 分隔线 -->
                                    <hr class="my-2">

                                    <!-- 可开票金额 - 突出显示 -->
                                    <div class="row">
                                        <div class="col-6 text-muted font-weight-bold">剩余可开票金额:</div>
                                        <div class="col-6 text-right">
                                            <span class="font-weight-bold text-success remaining-amount"
                                                style="font-size: 1.1rem;">¥{$remaining_amount}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>

                <!-- 历史发票申请记录 - 优化显示 -->
                <div class="card mb-3 mt-3" id="invoice-history-section" style="display: <empty name='invoice_history'>none<else/>block</empty>">
                    <div class="card-body p-3">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-history mr-2"></i>历史发票申请记录
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover mb-0">
                                <thead>
                                    <tr class="bg-light" style="font-size: 0.85rem;">
                                        <th>申请日期</th>
                                        <th>发票抬头</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody style="font-size: 0.85rem;">
                                    <volist name="invoice_history" id="invoice">
                                        <tr>
                                            <td>{$invoice.create_time|date="Y-m-d",###}</td>
                                            <td>{$invoice.invoice_title}</td>
                                            <td class="text-right font-weight-bold">¥{$invoice.amount}</td>
                                            <td>
                                                <if condition="$invoice.status eq 0">
                                                    <span class="badge badge-warning badge-pill">待审核</span>
                                                    <elseif condition="$invoice.status eq 201" />
                                                    <span class="badge badge-info badge-pill">审核中</span>
                                                    <elseif condition="$invoice.status eq 210" />
                                                    <span class="badge badge-primary badge-pill">OA审核</span>
                                                    <elseif condition="$invoice.status eq 220" />
                                                    <span class="badge badge-secondary badge-pill">待开票</span>
                                                    <elseif condition="$invoice.status eq 230" />
                                                    <span class="badge badge-success badge-pill">已开票</span>
                                                    <elseif
                                                        condition="$invoice.status eq 290 || $invoice.status eq 298 || $invoice.status eq 299" />
                                                    <span class="badge badge-danger badge-pill">已作废</span>
                                                    <else />
                                                    <span class="badge badge-secondary badge-pill">未知</span>
                                                </if>
                                            </td>
                                        </tr>
                                    </volist>
                                </tbody>
                            </table>
                        </div>

                        <!-- 历史发票汇总信息 - 更清晰的显示 -->
                        <div class="mt-3 p-2 bg-light rounded" style="font-size: 0.85rem;">
                            <div class="row">
                                <div class="col-6">
                                    <i class="fas fa-file-invoice mr-1"></i> 已开票总额:
                                    <span class="font-weight-bold">¥{$total_invoiced}</span>
                                </div>
                                <div class="col-6 text-right">
                                    <i class="fas fa-calculator mr-1"></i> 剩余可开票:
                                    <span class="font-weight-bold text-success">¥{$remaining_amount}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 右侧发票申请表单 -->
            <div class="col-lg-8">

                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-file-invoice mr-2"></i>发票申请表单</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-subtitle mb-4">请填写以下信息申请发票，带 <span class="text-danger">*</span> 的字段为必填项</p>

                        <form class="needs-validation" id="invoiceForm" action="{:U('Home/GuestInvoice/save')}" method="post" novalidate>

                            <!-- 基本信息 -->
                            <div class="form-section">
                                <h5 class="form-section-title">基本信息</h5>
                                <div class="form-row-flex">
                                    <div class="form-col-flex">
                                        <div class="form-group">
                                            <label for="invoice_title">发票抬头 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="invoice_title"
                                                name="invoice_title" placeholder="请输入发票抬头" required value="四川大学">
                                            <div class="invalid-feedback">
                                                请输入发票抬头
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-col-flex">
                                        <div class="form-group">
                                            <label for="buyer_tax_num">税号 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="buyer_tax_num"
                                                name="buyer_tax_num" placeholder="请输入税号" required value="*********">
                                            <div class="invalid-feedback">
                                                请输入税号
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row-flex">
                                    <div class="form-col-flex">
                                        <div class="form-group">
                                            <label for="amount">开票金额 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">¥</span>
                                                </div>
                                                <input type="number" step="0.01" min="0.01" id="amount" name="amount"
                                                    placeholder="请输入开票金额" required class="form-control amount-display">
                                                <div class="invalid-feedback">
                                                    请输入有效的开票金额
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">剩余可开票金额:
                                                <span
                                                    class="remaining-amount font-weight-bold">¥{$remaining_amount}</span></small>


                                        </div>
                                    </div>
                                    <div class="form-col-flex">
                                        <div class="form-group">
                                            <label for="goods_info">开票项目 <span class="text-danger">*</span></label>
                                            <select class="form-control" id="goods_info" name="goods_info" required>
                                                <foreach name="invoice_item_options" item="text" key="key">
                                                    <option value="{$key}">{$text}</option>
                                                </foreach>
                                            </select>
                                            <div class="invalid-feedback">
                                                请选择开票项目
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>发票类型 <span class="text-danger">*</span></label>
                                    <div>
                                        <foreach name="invoice_type_data" item="typeInfo" key="type">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" id="invoiceType{$type|ucfirst}" name="invoice_type"
                                                    class="custom-control-input" value="{$type}" required>
                                                <label class="custom-control-label"
                                                    for="invoiceType{$type|ucfirst}">{$typeInfo.text}</label>
                                            </div>
                                        </foreach>
                                        <div class="invalid-feedback">
                                            请选择发票类型
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 联系信息 -->
                            <div class="form-section">
                                <h5 class="form-section-title">联系信息</h5>
                                <div class="form-row-flex">
                                    <div class="form-col-flex">
                                        <div class="form-group">
                                            <label for="buyer_phone">买方手机号</label>
                                            <input type="tel" class="form-control" id="buyer_phone" name="buyer_phone"
                                                placeholder="请输入手机号码" value="13912345678">
                                        </div>
                                    </div>
                                    <div class="form-col-flex">
                                        <div class="form-group">
                                            <label for="buyer_email">买方邮箱 <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="buyer_email" name="buyer_email"
                                                placeholder="请输入邮箱地址" required value="<EMAIL>">
                                            <div class="invalid-feedback">
                                                请输入有效的邮箱地址
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 补充信息 -->
                            <div class="form-section">
                                <button type="button" class="btn btn-link text-primary p-0" data-toggle="collapse"
                                    data-target="#moreInfoFields" aria-expanded="false" aria-controls="moreInfoFields">
                                    <i class="feather-plus-circle mr-1"></i> 补充更多信息
                                </button>

                                <div class="collapse mt-3" id="moreInfoFields">
                                    <div class="bg-light py-3 px-3 mb-3" style="border-radius: 4px;">
                                        <h5 class="form-section-title">附加信息</h5>

                                        <div class="form-group">
                                            <label for="buyer_tel">座机电话号码</label>
                                            <input type="tel" class="form-control" id="buyer_tel" name="buyer_tel"
                                                placeholder="请输入座机电话号码（如：010-12345678）">
                                        </div>

                                        <div class="form-group">
                                            <label for="buyer_address">买方地址</label>
                                            <textarea class="form-control" id="buyer_address" name="buyer_address"
                                                rows="2" placeholder="请输入地址"></textarea>
                                        </div>

                                        <div class="form-row-flex">
                                                                                     <div class="form-col-flex">
                                                <div class="form-group">
                                                    <label for="buyer_account_name">买方账户名</label>
                                                    <input type="text" class="form-control" id="buyer_account_name"
                                                        name="buyer_account_name" placeholder="请输入账户名">
                                                </div>
                                            </div>
                                            <div class="form-col-flex">
                                                <div class="form-group">
                                                    <label for="buyer_account">买方账户</label>
                                                    <input type="text" class="form-control" id="buyer_account"
                                                        name="buyer_account" placeholder="请输入账户">
                                                </div>
                                            </div>
   
                                        </div>


                                    </div>
                                </div>
                            </div>

                            <div class="form-group mt-4">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="termsCheck" required>
                                    <label class="custom-control-label" for="termsCheck">我确认以上开票信息准确无误</label>
                                    <div class="invalid-feedback">
                                        您必须同意条款和条件才能继续
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <a href="{:U('Home/GuestInvoice/index')}" class="btn btn-outline-secondary mr-2">
                                    <i class="fas fa-arrow-left mr-1"></i> 返回
                                </a>
                                <button type="button" id="previewBtn" class="btn btn-primary px-4">
                                    <i class="fas fa-check mr-1"></i> 预览并提交
                                </button>
                                <button type="submit" id="submitBtn" class="d-none">提交</button>
                            </div>

                            <input type="hidden" id="order_id" name="order_id" value="{$pay_info.orderid}">
                        </form>

                    </div>
                </div>
            </div>
        </div>
        <!-- end row -->

    </div>
    <!-- End Page-content -->

    <!-- 确认模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-light">
                    <h5 class="modal-title" id="confirmModalLabel">
                        <i class="fas fa-check-circle text-success mr-2"></i> 确认发票申请信息
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-heading mr-1"></i> 发票抬头:</label>
                                <p id="modal-invoice_title" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-id-card mr-1"></i> 税号:</label>
                                <p id="modal-buyer_tax_num" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-money-bill mr-1"></i> 开票金额:</label>
                                <p id="modal-amount" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-list mr-1"></i> 开票项目:</label>
                                <p id="modal-goods_info" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-envelope mr-1"></i> 买方邮箱:</label>
                                <p id="modal-buyer_email" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-file-invoice mr-1"></i> 发票类型:</label>
                                <p id="modal-invoice_type" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-mobile-alt mr-1"></i> 买方手机号:</label>
                                <p id="modal-buyer_phone" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold"><i class="fas fa-phone mr-1"></i> 座机电话:</label>
                                <p id="modal-buyer_tel" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div id="additionalInfoSection" class="d-none">
                        <hr>
                        <h6 class="font-weight-bold mb-3"><i class="fas fa-info-circle mr-1"></i> 附加信息</h6>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="font-weight-bold"><i class="fas fa-map-marker-alt mr-1"></i>
                                        买方地址:</label>
                                    <p id="modal-buyer_address" class="mb-0"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold"><i class="fas fa-university mr-1"></i> 买方账户:</label>
                                    <p id="modal-buyer_account" class="mb-0"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold"><i class="fas fa-user mr-1"></i> 买方账户名:</label>
                                    <p id="modal-buyer_account_name" class="mb-0"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold"><i class="fas fa-building mr-1"></i> 销方公司名称:</label>
                                    <p id="modal-saler_company" class="mb-0"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="font-weight-bold"><i class="fas fa-comment mr-1"></i> 备注信息:</label>
                                    <p id="modal-remark" class="mb-0"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                        <i class="fas fa-arrow-left mr-1"></i> 返回修改
                    </button>
                    <button type="button" id="confirmSubmit" class="btn btn-success">
                        <i class="fas fa-check mr-1"></i> 确认提交
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 成功模态框 -->
    <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-labelledby="successModalLabel"
        aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="successModalLabel">
                        <i class="fas fa-check-circle mr-2"></i> 发票申请成功
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" id="closeSuccessModal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="mb-3">您的发票申请已成功提交</h4>
                    <p class="mb-1">我们将尽快处理您的申请，请留意邮箱通知。</p>
                    <p class="text-muted">您可以随时通过订单号和邮箱查询发票状态。</p>

                    <div id="remaining-amount-container" class="alert alert-info mt-3" role="alert">
                        <i class="fas fa-info-circle mr-2"></i> 剩余可开票金额: <span id="remaining-amount-display" class="font-weight-bold">¥0.00</span>
                    </div>
                </div>
                <div class="modal-footer" id="success-modal-footer">
                    <!-- 动态生成按钮区域 -->
                </div>
            </div>
        </div>
    </div>
</block>

<block name="scripts">

    <!-- 自定义验证脚本 -->
    <script>
        (function () {
            'use strict';

            document.addEventListener('DOMContentLoaded', function () {
                // 定义常量
                const ERROR_MESSAGES = {
                    INVALID_AMOUNT: '开票金额必须大于0',
                    EXCEED_AMOUNT: '开票金额不能超过剩余可开票金额'
                };

                // 发票类型映射 - 从控制器获取
                const INVOICE_TYPE_MAP = <php>echo '{'.$invoice_type_map_js.'}';</php>;

                // 开票项目映射 - 从控制器获取
                const INVOICE_ITEM_MAP = <php>echo '{'.$invoice_item_map_js.'}';</php>;

                // 获取元素和变量
                const form = document.querySelector('.needs-validation');
                const amountInput = document.getElementById('amount');
                const previewBtn = document.getElementById('previewBtn');
                const confirmSubmit = document.getElementById('confirmSubmit');
                const submitBtn = document.getElementById('submitBtn');
                const remainingAmount = parseFloat(document.querySelector('.remaining-amount').textContent.replace('¥', '')) || 0;

                // 设置金额输入框默认值
                if (remainingAmount > 0) {
                    amountInput.value = remainingAmount;
                }

                // 验证金额函数
                function validateAmount(amount) {
                    if (isNaN(amount) || amount <= 0) {
                        return ERROR_MESSAGES.INVALID_AMOUNT;
                    } else if (amount > remainingAmount) {
                        return ERROR_MESSAGES.EXCEED_AMOUNT;
                    }
                    return '';
                }

                // 表单验证函数
                function validateForm() {
                    let isValid = form.checkValidity();

                    if (amountInput) {
                        const amount = parseFloat(amountInput.value);
                        const errorMsg = validateAmount(amount);
                        amountInput.setCustomValidity(errorMsg);
                        if (errorMsg) isValid = false;
                    }

                    form.classList.add('was-validated');
                    return isValid;
                }

                // 获取表单数据
                function getFormData() {
                    const formElements = {
                        'invoice_title': '',
                        'buyer_tax_num': '',
                        'amount': '',
                        'goods_info': '',
                        'buyer_email': '',
                        'buyer_phone': '未填写',
                        'buyer_tel': '未填写',
                        'buyer_address': '未填写',
                        'buyer_account': '未填写',
                        'buyer_account_name': '未填写',
                        'saler_company': '未填写',
                        'remark': '未填写'
                    };

                    // 收集表单数据
                    Object.keys(formElements).forEach(id => {
                        const element = document.getElementById(id);
                        if (element) {
                            formElements[id] = element.value.trim() || formElements[id];
                        }
                    });

                    // 处理发票类型
                    const invoiceTypeEl = document.querySelector('input[name="invoice_type"]:checked');
                    const invoiceType = invoiceTypeEl ? invoiceTypeEl.value : '';

                    return {
                        invoiceTitle: formElements.invoice_title,
                        buyerTaxNum: formElements.buyer_tax_num,
                        amount: formElements.amount,
                        invoiceItem: formElements.goods_info,
                        buyerEmail: formElements.buyer_email,
                        invoiceType: invoiceType,
                        buyerPhone: formElements.buyer_phone,
                        buyerTel: formElements.buyer_tel,
                        buyerAddress: formElements.buyer_address,
                        buyerAccount: formElements.buyer_account,
                        buyerAccountName: formElements.buyer_account_name,
                        salerCompany: formElements.saler_company,
                        remark: formElements.remark
                    };
                }

                // 填充模态框内容
                function populateModalContent(data) {
                    // 设置基本字段
                    const modalFields = [
                        { id: 'modal-invoice_title', value: data.invoiceTitle },
                        { id: 'modal-buyer_tax_num', value: data.buyerTaxNum },
                        { id: 'modal-amount', value: '¥' + (data.amount || '0.00') },
                        { id: 'modal-goods_info', value: INVOICE_ITEM_MAP[data.invoiceItem] || '未选择' },
                        { id: 'modal-buyer_email', value: data.buyerEmail },
                        { id: 'modal-invoice_type', value: INVOICE_TYPE_MAP[data.invoiceType] || '未选择' },
                        { id: 'modal-buyer_phone', value: data.buyerPhone },
                        { id: 'modal-buyer_tel', value: data.buyerTel },
                        { id: 'modal-buyer_address', value: data.buyerAddress },
                        { id: 'modal-buyer_account', value: data.buyerAccount },
                        { id: 'modal-buyer_account_name', value: data.buyerAccountName },
                        { id: 'modal-saler_company', value: data.salerCompany },
                        { id: 'modal-remark', value: data.remark }
                    ];

                    // 更新模态框内容
                    modalFields.forEach(field => {
                        const element = document.getElementById(field.id);
                        if (element) {
                            element.innerText = field.value || '未填写';
                        }
                    });

                    // 显示/隐藏附加信息部分
                    const additionalInfoSection = document.getElementById('additionalInfoSection');
                    if (data.buyerTel !== '未填写' || data.buyerAddress !== '未填写' || data.buyerAccount !== '未填写' || data.buyerAccountName !== '未填写' || data.salerCompany !== '未填写' || data.remark !== '未填写') {
                        additionalInfoSection.classList.remove('d-none');
                    } else {
                        additionalInfoSection.classList.add('d-none');
                    }
                }

                // 事件监听器
                previewBtn.addEventListener('click', function (event) {
                    if (!validateForm()) {
                        event.preventDefault();
                        return;
                    }

                    try {
                        populateModalContent(getFormData());
                        $(document.getElementById('confirmModal')).modal('show');
                    } catch (error) {
                        console.error('预览时出错:', error);
                        alert('请检查所有必填项是否已填写。');
                    }
                });

                confirmSubmit.addEventListener('click', function () {
                    // 禁用按钮，防止重复提交
                    confirmSubmit.disabled = true;
                    confirmSubmit.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> 提交中...';

                    // 获取表单数据
                    const formData = new FormData(form);

                    // 发送AJAX请求
                    $.ajax({
                        url: '{:U("Home/GuestInvoice/saveAjax")}',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            // 恢复按钮状态
                            confirmSubmit.disabled = false;
                            confirmSubmit.innerHTML = '<i class="fas fa-check mr-1"></i> 确认提交';

                            // 关闭确认模态框
                            $(document.getElementById('confirmModal')).modal('hide');

                            if (response.status === 1) {
                                // 更新剩余可开票金额显示
                                let remainingAmount = 0;
                                if (response.invoice_data && response.invoice_data.remaining_amount) {
                                    remainingAmount = parseFloat(response.invoice_data.remaining_amount);
                                    document.getElementById('remaining-amount-display').textContent =
                                        '¥' + response.invoice_data.remaining_amount;
                                }

                                // 根据剩余金额动态生成按钮
                                const footerEl = document.getElementById('success-modal-footer');
                                footerEl.innerHTML = ''; // 清空现有按钮

                                // 创建按钮的辅助函数
                                function createButton(type, className, icon, text, onClick) {
                                    const btn = document.createElement(type === 'link' ? 'a' : 'button');
                                    if (type === 'link') {
                                        btn.href = onClick;
                                    } else {
                                        btn.type = 'button';
                                        btn.onclick = onClick;
                                    }
                                    btn.className = className;
                                    btn.innerHTML = icon ? `<i class="${icon} mr-1"></i> ${text}` : text;
                                    return btn;
                                }

                                // 添加"返回首页"按钮
                                const homeBtn = createButton('link', 'btn btn-outline-secondary', 'fas fa-home', '返回首页', '{:U("Home/GuestInvoice/index")}');
                                footerEl.appendChild(homeBtn);

                                // 如果有剩余金额，添加"继续开票"按钮
                                if (remainingAmount > 0) {
                                    // 获取订单号
                                    const currentOrderId = response.invoice_data && response.invoice_data.order_id
                                        ? response.invoice_data.order_id
                                        : (document.getElementById('order_id') ? document.getElementById('order_id').value : '');

                                    if (currentOrderId) {
                                        const continueBtn = createButton('button', 'btn btn-success ml-2', 'fas fa-plus-circle', '继续开票', function() {
                                            $(document.getElementById('successModal')).modal('hide');
                                            // 使用延迟确保模态框完全关闭
                                            setTimeout(function() {
                                                window.location.href = '{:U("Home/GuestInvoice/query")}?order_id=' + encodeURIComponent(currentOrderId);
                                            }, 300);
                                        });
                                        footerEl.appendChild(continueBtn);
                                    } else {
                                        console.error('无法获取订单号');
                                    }
                                }

                                // 添加"查询发票状态"按钮
                                const statusBtn = createButton('link', 'btn btn-primary ml-2', 'fas fa-search', '查询发票状态', '{:U("Home/GuestInvoice/status")}');
                                footerEl.appendChild(statusBtn);

                                // 显示成功模态框
                                setTimeout(function() {
                                    $(document.getElementById('successModal')).modal('show');
                                }, 500);

                                // 添加模态框关闭和隐藏事件，刷新页面
                                function refreshPageAfterDelay() {
                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 300);
                                }

                                // 点击关闭按钮时刷新页面
                                document.getElementById('closeSuccessModal').addEventListener('click', refreshPageAfterDelay);

                                // 通过其他方式关闭模态框时也刷新页面
                                $(document.getElementById('successModal')).on('hidden.bs.modal', refreshPageAfterDelay);
                            } else {
                                // 显示错误信息
                                Swal.fire({
                                    icon: 'error',
                                    title: '提交失败',
                                    text: response.message || '发票申请失败，请稍后重试',
                                    confirmButtonText: '确定'
                                });
                            }
                        },
                        error: function() {
                            // 恢复按钮状态
                            confirmSubmit.disabled = false;
                            confirmSubmit.innerHTML = '<i class="fas fa-check mr-1"></i> 确认提交';

                            // 显示错误信息
                            Swal.fire({
                                icon: 'error',
                                title: '提交失败',
                                text: '网络错误，请稍后重试',
                                confirmButtonText: '确定'
                            });
                        }
                    });
                });

                form.addEventListener('submit', function (event) {
                    // 阻止表单默认提交行为，改为使用AJAX提交
                    event.preventDefault();

                    if (!validateForm()) {
                        return;
                    }

                    // 显示确认模态框
                    populateModalContent(getFormData());
                    $(document.getElementById('confirmModal')).modal('show');
                });

                amountInput.addEventListener('input', function () {
                    const errorMsg = validateAmount(parseFloat(this.value));
                    this.setCustomValidity(errorMsg);
                });
            });
        })();
    </script>
</block>